# Aggressive SCSS Cleanup Report

Generated on: 2025-07-24T16:54:11.856Z

## 📊 Summary

- **Files removed**: 16
- **Imports removed**: 16
- **Used classes found**: 535
- **Errors encountered**: 1
- **Backup location**: /home/<USER>/gammastack/betshop/Starterkit/aggressive-scss-backup

## 🗑️ Removed Files

- pages/_chat.scss
- pages/_ecommerce.scss
- pages/_file-manager.scss
- pages/_mail.scss
- pages/_task.scss
- pages/_landing.scss
- menu-styles/_closed_menu.scss
- menu-styles/_detached_menu.scss
- menu-styles/_double_menu.scss
- menu-styles/_icon_click.scss
- menu-styles/_icon_hover.scss
- menu-styles/_icon_overlay.scss
- menu-styles/_icontext.scss
- menu-styles/_menu_click.scss
- menu-styles/_menu_hover.scss
- menu-styles/_vertical.scss

## 📋 Removed Imports

- @forward "pages/chat"
- @forward "pages/ecommerce"
- @forward "pages/file-manager"
- @forward "pages/mail"
- @forward "pages/task"
- @forward "pages/landing"
- @forward "menu-styles/closed_menu"
- @forward "menu-styles/detached_menu"
- @forward "menu-styles/double_menu"
- @forward "menu-styles/icon_click"
- @forward "menu-styles/icon_hover"
- @forward "menu-styles/icon_overlay"
- @forward "menu-styles/icontext"
- @forward "menu-styles/menu_click"
- @forward "menu-styles/menu_hover"
- @forward "menu-styles/vertical"

## 🎯 Used Classes (Sample)

- .authentication
- .coming-soon
- .justify-center
- .g-0
- .my-auto
- .col-span-11
- .px-2
- .authentication-cover
- .overflow-hidden
- .box
- .border
- .my-3
- .aunthentication-cover-content
- .grid-cols-12
- .items-center
- .mx-0
- .col-span-12
- .mb-4
- .authentication-brand
- .mb-2

*... and 515 more*

## ❌ Errors

- **build_test**: Command failed: npm run build:fast
<w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (1751kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
<w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (1751kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
<w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (1753kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
Failed to compile.

./app/globals.scss
Module not found: Can't resolve './remixicon.eot?t=1708865856766'

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./app/globals.scss

./app/globals.scss
Module not found: Can't resolve './remixicon.woff2?t=1708865856766'

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./app/globals.scss

./app/globals.scss
Module not found: Can't resolve './remixicon.woff?t=1708865856766'

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./app/globals.scss

./app/globals.scss
Module not found: Can't resolve './remixicon.ttf?t=1708865856766'

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./app/globals.scss

./app/globals.scss
Module not found: Can't resolve './remixicon.svg?t=1708865856766'

https://nextjs.org/docs/messages/module-not-found

Import trace for requested module:
./app/globals.scss


> Build failed because of webpack errors


## 🔄 Rollback Instructions

If issues are detected, restore from backup:
```bash
cp -r /home/<USER>/gammastack/betshop/Starterkit/aggressive-scss-backup/scss/* public/assets/scss/
```

---
*Generated by Aggressive SCSS Cleanup*
