'use client';

import React, { useState } from 'react';
import ToastNotification from '../../shared/UI/notifications/ToastNotification';

const TestToastNotificationPage = () => {
  const [toasts, setToasts] = useState<Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
  }>>([]);

  const addToast = (type: 'success' | 'error' | 'warning' | 'info') => {
    const id = Date.now().toString();
    const toastData = {
      success: {
        title: 'Success!',
        message: 'Your action was completed successfully. Everything is working as expected.'
      },
      error: {
        title: 'Error!',
        message: 'Something went wrong. Please try again or contact support if the issue persists.'
      },
      warning: {
        title: 'Warning!',
        message: 'Please be careful. This action may have unintended consequences.'
      },
      info: {
        title: 'Information',
        message: 'Here is some important information you should know about this feature.'
      }
    };

    const newToast = {
      id,
      type,
      ...toastData[type]
    };

    setToasts(prev => [...prev, newToast]);
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Toast Notification Test</h1>

        <div className="bg-elevated p-6 rounded-lg mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Test Toast Notifications</h2>
          <div className="flex gap-4 flex-wrap">
            <button
              onClick={() => addToast('success')}
              className="px-4 py-2 bg-success-notification-border text-white rounded hover:opacity-80 transition-opacity border border-success-notification-border"
            >
              Show Success Toast
            </button>
            <button
              onClick={() => addToast('error')}
              className="px-4 py-2 bg-error-notification-border text-white rounded hover:opacity-80 transition-opacity border border-error-notification-border"
            >
              Show Error Toast
            </button>
            <button
              onClick={() => addToast('warning')}
              className="px-4 py-2 bg-warning-notification-border text-white rounded hover:opacity-80 transition-opacity border border-warning-notification-border"
            >
              Show Warning Toast
            </button>
            <button
              onClick={() => addToast('info')}
              className="px-4 py-2 bg-info-notification-border text-white rounded hover:opacity-80 transition-opacity border border-info-notification-border"
            >
              Show Info Toast
            </button>
          </div>
        </div>

        <div className="bg-elevated p-6 rounded-lg">
          <h2 className="text-xl font-semibold text-white mb-4">Color Test</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Success Colors */}
            <div className="space-y-2">
              <h3 className="text-white font-medium">Success Colors</h3>
              <div className="bg-success-notification p-3 rounded text-white text-sm border border-success-notification-border">
                bg-success-notification
              </div>
              <div className="border-2 border-success-notification-border p-3 rounded text-white text-sm">
                border-success-notification-border
              </div>
              <div className="text-success-notification-icon text-sm font-medium">
                text-success-notification-icon
              </div>
              <div className="text-success-message text-sm">
                text-success-message
              </div>
            </div>

            {/* Error Colors */}
            <div className="space-y-2">
              <h3 className="text-white font-medium">Error Colors</h3>
              <div className="bg-error-notification p-3 rounded text-white text-sm border border-error-notification-border">
                bg-error-notification
              </div>
              <div className="border-2 border-error-notification-border p-3 rounded text-white text-sm">
                border-error-notification-border
              </div>
              <div className="text-error-notification-icon text-sm font-medium">
                text-error-notification-icon
              </div>
              <div className="text-error-message text-sm">
                text-error-message
              </div>
            </div>

            {/* Warning Colors */}
            <div className="space-y-2">
              <h3 className="text-white font-medium">Warning Colors</h3>
              <div className="bg-warning-notification p-3 rounded text-white text-sm border border-warning-notification-border">
                bg-warning-notification
              </div>
              <div className="border-2 border-warning-notification-border p-3 rounded text-white text-sm">
                border-warning-notification-border
              </div>
              <div className="text-warning-notification-icon text-sm font-medium">
                text-warning-notification-icon
              </div>
              <div className="text-warning-message text-sm">
                text-warning-message
              </div>
            </div>

            {/* Info Colors */}
            <div className="space-y-2">
              <h3 className="text-white font-medium">Info Colors</h3>
              <div className="bg-info-notification p-3 rounded text-white text-sm border border-info-notification-border">
                bg-info-notification
              </div>
              <div className="border-2 border-info-notification-border p-3 rounded text-white text-sm">
                border-info-notification-border
              </div>
              <div className="text-info-notification-icon text-sm font-medium">
                text-info-notification-icon
              </div>
              <div className="text-info-message text-sm">
                text-info-message
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Toast Container */}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        {toasts.map((toast) => (
          <ToastNotification
            key={toast.id}
            id={toast.id}
            type={toast.type}
            title={toast.title}
            message={toast.message}
            onClose={removeToast}
          />
        ))}
      </div>
    </div>
  );
};

export default TestToastNotificationPage;
