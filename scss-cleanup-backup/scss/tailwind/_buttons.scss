

  /* Start Buttons Styles */
  .ti-btn {
      @apply py-[0.375rem] px-3 inline-flex justify-center items-center gap-2 rounded-sm border border-transparent font-medium focus:ring-0 focus:outline-none focus:ring-offset-0 transition-all text-sm m-1 ms-0;

      &.ti-btn-outline {
          @apply border;
      }

      &.ti-btn-disabled {
          @apply cursor-not-allowed opacity-50;
      }
  }
  .ti-btn.ti-btn-sm {
    @apply py-[0.26rem] px-2 rounded-[0.25rem] text-[0.75rem];
  }
  .ti-btn.ti-btn-lg {
    @apply py-[0.65rem] px-4 rounded-[0.3rem] text-[0.95rem];
  }
  .ti-btn-group .ti-btn {
    @apply py-[0.45rem] px-3;
  }
  .ti-btn-group-lg .ti-btn {
    @apply py-[0.65rem] px-4;
  }
  .ti-btn-group-sm .ti-btn {
    @apply py-[0.25rem] px-2;
  }
  .ti-btn-icon {
    @apply w-[2.1rem] h-[2.1rem] text-[0.95rem] p-[0.375rem] shrink-0;

    i {
      @apply p-0 my-0 -mx-2;
      padding: 0rem;
      margin: 0 -0.5rem;
    }
  }

  .ti-btn-icon.ti-btn-sm {
    @apply w-[1.75rem] h-[1.75rem] text-[0.8rem] p-[0.1875rem];
  }

  .ti-btn-icon.ti-btn-lg {
    @apply w-[2.75rem] h-[2.75rem] text-[1.2rem] p-[0.5rem];
  }
  .ti-btn-list button,
.ti-btn-list div,
.ti-btn-list a,
.ti-btn-list input {
  @apply mt-0 mb-[0.375rem] ms-0 me-[0.375rem];
}
.ti-btn-list {
  @apply -mb-2;
}
  .ti-btn-group {
      @apply py-3 px-4 inline-flex justify-center items-center gap-2 -ms-px first:rounded-s-sm first:ms-0 last:rounded-e-sm border border-defaultborder dark:border-defaultborder/10 font-medium align-middle focus:z-10 focus:outline-none focus:ring-0 transition-all text-sm;
  }

  .ti-btn-primary {
      @apply bg-primary text-white hover:bg-primary focus:ring-primary dark:focus:ring-offset-white/10;
  }

  .ti-btn-secondary {
      @apply bg-secondary text-white hover:bg-secondary focus:ring-secondary dark:focus:ring-offset-white/10;
  }

  .ti-btn-warning {
      @apply bg-warning text-white hover:bg-warning focus:ring-warning dark:focus:ring-offset-white/10;
  }

  .ti-btn-success {
      @apply bg-success text-white hover:bg-success focus:ring-success dark:focus:ring-offset-white/10;
  }

  .ti-btn-light {
      @apply bg-light dark:bg-light text-defaulttextcolor dark:text-defaulttextcolor/80 hover:bg-[#f2f2f3] focus:ring-gray-100 dark:focus:ring-offset-white/10;
  }

  .ti-btn-dark {
      @apply bg-gray-900 text-gray-100 hover:bg-gray-900 focus:ring-white/10 dark:focus:ring-offset-white/10 #{!important};
  }

  .ti-btn-info {
      @apply bg-info text-white hover:bg-info focus:ring-info dark:focus:ring-offset-white/10;
  }

  .ti-btn-danger {
      @apply bg-danger text-white hover:bg-danger focus:ring-danger dark:focus:ring-offset-white/10;
  }

  .ti-btn-orange {
    @apply bg-orangemain text-white hover:bg-orangemain focus:ring-orangemain dark:focus:ring-offset-white/10;
  }

  .ti-btn-purple {
    @apply bg-purplemain text-white hover:bg-purplemain focus:ring-purplemain dark:focus:ring-offset-white/10;
  }

  .ti-btn-teal {
    @apply bg-tealmain text-white hover:bg-tealmain focus:ring-tealmain dark:focus:ring-offset-white/10;
  }

  .ti-btn-outline-primary {
      @apply border-primary text-primary hover:text-white hover:bg-primary hover:border-primary focus:ring-primary dark:focus:ring-offset-white/10;
  }

  .ti-btn-outline-secondary {
      @apply border-secondary text-secondary hover:text-white hover:bg-secondary hover:border-secondary focus:ring-secondary dark:focus:ring-offset-white/10;
  }
  
  .ti-btn-outline-danger {
      @apply border-danger text-danger hover:text-white hover:bg-danger hover:border-danger focus:ring-danger dark:focus:ring-offset-white/10;
  }
  
  .ti-btn-outline-warning {
      @apply border-warning text-warning hover:text-white hover:bg-warning hover:border-warning focus:ring-warning dark:focus:ring-offset-white/10;
  }
  
  .ti-btn-outline-info {
      @apply border-info text-info hover:text-white hover:bg-info hover:border-info focus:ring-info dark:focus:ring-offset-white/10;
  }
  
  .ti-btn-outline-success {
      @apply border-success text-success hover:text-white hover:bg-success hover:border-success focus:ring-success dark:focus:ring-offset-white/10;
  }
  
  .ti-btn-outline-light {
      @apply border-defaultborder text-defaulttextcolor dark:text-defaulttextcolor/80 dark:border-defaultborder/10 hover:bg-gray-200 dark:hover:bg-light hover:border-defaultborder focus:ring-gray-100 dark:focus:ring-offset-white/10;
  }

  .ti-btn-outline-dark {
      @apply border-gray-900 text-gray-900 hover:text-white hover:bg-gray-900 hover:border-gray-900 dark:border-white/10 dark:text-white/70 focus:ring-white/10 dark:focus:ring-offset-white/10;
  }

  .ti-btn-ghost-primary {
      @apply text-primary hover:bg-primary/10 focus:ring-primary dark:focus:ring-offset-white/10;
  }
  .ti-btn-ghost-secondary {
      @apply text-secondary hover:bg-secondary/10 focus:ring-secondary dark:focus:ring-offset-white/10;
  }
  .ti-btn-ghost-warning {
      @apply text-warning hover:bg-warning/10 focus:ring-warning dark:focus:ring-offset-white/10;
  }
  .ti-btn-ghost-info {
      @apply text-info hover:bg-info/10 focus:ring-info dark:focus:ring-offset-white/10;
  }
  .ti-btn-ghost-danger {
      @apply text-danger hover:bg-danger/10 focus:ring-danger dark:focus:ring-offset-white/10;
  }
  .ti-btn-ghost-success {
      @apply text-success hover:bg-success/10 focus:ring-success dark:focus:ring-offset-white/10;
  }
  .ti-btn-ghost-purple {
    @apply text-purplemain hover:bg-purplemain/10 focus:ring-purplemain dark:focus:ring-offset-white/10;
}
  .ti-btn-ghost-teal {
    @apply text-tealmain hover:bg-tealmain/10 focus:ring-tealmain dark:focus:ring-offset-white/10;
}
  .ti-btn-ghost-orange {
    @apply text-orangemain hover:bg-orangemain/10 focus:ring-orangemain dark:focus:ring-offset-white/10;
}
  .ti-btn-ghost-light {
      @apply text-gray-500 hover:bg-gray-100 focus:ring-gray-100 dark:focus:ring-offset-white/10 dark:hover:bg-bodybg;
  }
  .ti-btn-ghost-dark {
      @apply text-gray-900 hover:bg-gray-900/10 focus:ring-white/10 dark:focus:ring-offset-white/10 dark:text-white/70 dark:hover:bg-black/20;
  }
  .ti-btn-soft-primary {
      @apply bg-primary/[0.15] text-primary hover:text-white hover:bg-primary ring-offset-white focus:ring-primary dark:focus:ring-offset-white/10;
  }
  .ti-btn-soft-primary1{
    @apply bg-primarytint1color/[0.15] text-primarytint1color hover:text-white hover:bg-primarytint1color ring-offset-white focus:ring-primary dark:focus:ring-offset-white/10;
  }
  .ti-btn-soft-primary2{
    @apply bg-primarytint2color/[0.15] text-primarytint2color hover:text-white hover:bg-primarytint2color ring-offset-white focus:ring-primary dark:focus:ring-offset-white/10;
  }
  .ti-btn-soft-primary3{
    @apply bg-primarytint3color/[0.15] text-primarytint3color hover:text-white hover:bg-primarytint3color ring-offset-white focus:ring-primary dark:focus:ring-offset-white/10;
  }
  .ti-btn-soft-secondary {
      @apply bg-secondary/[0.15]  text-secondary hover:text-white hover:bg-secondary ring-offset-white focus:ring-secondary dark:focus:ring-offset-white/10;
  }
  .ti-btn-soft-warning {
      @apply bg-warning/[0.15]  text-warning hover:text-white hover:bg-warning ring-offset-white focus:ring-warning dark:focus:ring-offset-white/10;
  }
  .ti-btn-soft-danger {
      @apply bg-danger/[0.15]  text-danger hover:text-white hover:bg-danger ring-offset-white focus:ring-danger dark:focus:ring-offset-white/10;
  }
  .ti-btn-soft-info {
      @apply bg-info/[0.15]  text-info hover:text-white hover:bg-info ring-offset-white focus:ring-info dark:focus:ring-offset-white/10;
  }
  .ti-btn-soft-orange {
    @apply bg-orangemain/[0.15]  text-orangemain hover:text-white hover:bg-orangemain ring-offset-white focus:ring-orangemain dark:focus:ring-offset-white/10;
}
.ti-btn-soft-purple {
    @apply bg-purplemain/[0.15] text-purplemain hover:text-white hover:bg-purplemain ring-offset-white focus:ring-purplemain dark:focus:ring-offset-white/10;
}
.ti-btn-soft-teal {
    @apply bg-tealmain/[0.15]  text-tealmain hover:text-white hover:bg-tealmain ring-offset-white focus:ring-tealmain dark:focus:ring-offset-white/10;
}
  .ti-btn-soft-success {
      @apply bg-success/[0.15]  text-success hover:text-white hover:bg-success ring-offset-white focus:ring-success dark:focus:ring-offset-white/10;
  }
  .ti-btn-soft-light {
      @apply bg-gray-100 dark:bg-gray-100/10 text-gray-500 dark:text-white dark:hover:text-gray-800 hover:text-gray-800 hover:bg-gray-200 dark:hover:bg-gray-200 ring-offset-white focus:ring-success dark:focus:ring-offset-white/10;
  }
  .ti-btn-soft-dark {
      @apply bg-gray-900/10 text-gray-900 hover:text-white hover:bg-gray-900 ring-offset-white focus:ring-white/10 dark:focus:ring-offset-white/10 dark:text-white/70;
  }

  .ti-btn-primary-gradient{
    @apply bg-gradient-to-r from-primary to-secondary text-white border-0;
  }
  .ti-btn-secondary-gradient{
    @apply bg-gradient-to-r from-secondary to-[#7289FF] text-white border-0;
  }
  .ti-btn-success-gradient{
    @apply bg-gradient-to-r from-success to-[#009CA4] text-white border-0;
  }
  .ti-btn-danger-gradient{
    @apply bg-gradient-to-r from-danger to-[#DE4980] text-white border-0;
  }
  .ti-btn-warning-gradient{
    @apply bg-gradient-to-r from-warning to-[#9EA53C] text-white border-0;
  }
  .ti-btn-info-gradient{
    @apply bg-gradient-to-r from-info to-[#4990E1] text-white border-0;
  }
  .ti-btn-orange-gradient{
    @apply bg-gradient-to-r from-orangemain to-[#E5647E] text-white border-0;
  }
  .ti-btn-purple-gradient{
    @apply bg-gradient-to-r from-purplemain to-[#0086FF] text-white border-0;
  }
  .ti-btn-teal-gradient{
    @apply bg-gradient-to-r from-tealmain to-[#3AE3C7] text-white border-0;
  }
  /* End Buttons Styles */

  .ti-btn.ti-btn-w-sm {
    @apply min-w-[6.975rem] #{!important};
}
.ti-btn.ti-btn-w-md {
    @apply min-w-[8.125rem] #{!important};
}
.ti-btn.ti-btn-w-lg{
    @apply min-w-[9.375rem];
}

  .custom-button {
    @apply relative ps-[2.75rem];
    .custom-ti-btn-icons {
      @apply shadow-[0_0_1px_0.25rem_rgba(0,0,0,0.1)] absolute -start-[0.125rem] top-0 bg-white flex items-center justify-center overflow-hidden p-[0.375rem] rounded-[3.125rem]
          text-[1rem] w-[2.25rem] h-[2.25rem];
      i {
        @apply absolute;
      } 
    }
  }
  .ti-btn-border-down.ti-btn-soft-teal {
    @apply border-b-[0.1875rem] border-t-0 border-x-0 border-solid border-b-tealmain #{!important};
  }
  .ti-btn-border-start.ti-btn-soft-secondary {
    @apply border-s-[0.1875rem] border-t-0 border-b-0 border-e-0 border-solid border-s-secondary #{!important};
  }
  .ti-btn-border-end.ti-btn-soft-purple {
    @apply border-e-[0.1875rem] border-t-0 border-s-0 border-b-0 border-solid border-e-purplemain #{!important};
  }
  .ti-btn-border-top.ti-btn-soft-warning {
    @apply border-t-[0.1875rem] border-b-0 border-x-0 border-solid border-t-warning #{!important};
  }
  .ti-btn-hover {
    @apply relative;
    &.ti-btn-hover-animate {
      @apply transition-all duration-[0.2s] ease-linear delay-[0s] before:content-["\F417"] before:text-[0.8125rem] before:absolute 
          before:flex before:items-center before:justify-center before:end-0 before:top-0 before:opacity-0 before:h-full before:w-[2rem] before:transition-all
          before:duration-[0.2s] before:ease-linear before:delay-[0s] before:font-bootstrap;
      &:hover {
        @apply pe-8 before:opacity-[1] before:indent-0;
      }
    }
  }
  .ti-btn-darken-hover {
    @apply relative;
    &:hover {
      @apply before:absolute before:w-full before:h-full before:bg-black/[0.25] before:top-0 before:start-0;
    }
  }
  .ti-btn-loader i {
    @apply animate-spin #{!important};
  }
  .ti-btn-group-vertical > .ti-btn,
  .ti-btn-group > .ti-btn {
    @apply relative flex-grow;
  }
  .btn-check {
    @apply absolute pointer-events-none sr-only;
  }
  .ti-btn-list a {
    @apply ms-0 me-1.5 mt-0 mb-1.5 #{!important};
  }
  .label-ti-btn {
    @apply relative ps-[2.6rem];
  }
  .label-ti-btn-icon {
    @apply absolute w-[2.25rem] text-[1rem] flex items-center justify-center -start-0 -top-0 -bottom-0 bg-white/20;
  }
  .label-ti-btn.label-end {
    @apply ps-4 pe-[2.6rem] #{!important};
    .label-ti-btn-icon {
      @apply -end-0 start-auto;
    }
  }

  /* Start:: Social Buttons */
.ti-btn-facebook {
    @apply bg-facebook text-white border border-solid border-facebook;
    &:hover,
    &:focus,
    &:active {
      @apply bg-facebook text-white border border-solid border-facebook #{!important};
    }
  }
  .ti-btn-google {
    @apply bg-google text-white border border-solid border-google;
    &:hover,
    &:focus,
    &:active {
      @apply bg-google text-white border border-solid border-google #{!important};
    }
  }
  .ti-btn-twitter {
    @apply bg-twitter text-white border border-solid border-twitter;
    &:hover,
    &:focus,
    &:active {
      @apply bg-twitter text-white border border-solid border-twitter #{!important};
    }
  }
  .ti-btn-github {
    @apply bg-github text-white border border-solid border-github;
    &:hover,
    &:focus,
    &:active {
      @apply bg-github text-white border border-solid border-github #{!important};
    }
  }
  .ti-btn-youtube {
    @apply bg-youtube text-white border border-solid border-youtube;
    &:hover,
    &:focus,
    &:active {
      @apply bg-youtube text-white border border-solid border-youtube #{!important};
    }
  }
  .ti-btn-instagram {
    @apply bg-[#f09433] text-white border border-solid border-transparent;
    background: -moz-linear-gradient(
      45deg,
      #f09433 0%,
      #e6683c 25%,
      #dc2743 50%,
      #cc2366 75%,
      #bc1888 100%
    );
    background: -webkit-linear-gradient(
      45deg,
      #f09433 0%,
      #e6683c 25%,
      #dc2743 50%,
      #cc2366 75%,
      #bc1888 100%
    );
    background: linear-gradient(
      45deg,
      #f09433 0%,
      #e6683c 25%,
      #dc2743 50%,
      #cc2366 75%,
      #bc1888 100%
    );
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f09433', endColorstr='#bc1888',GradientType=1 );
    &:hover,
    &:focus,
    &:active {
      @apply text-white border border-solid border-transparent;
    }
  }
  .label-ti-btn {
    @apply relative ps-[2.6rem] #{!important};
}

.btn-check+.ti-btn.ti-btn-outline-primary:hover,
.btn-check:active+.ti-btn-outline-primary,
.btn-check:checked+.ti-btn-outline-primary,
.ti-btn-outline-primary.active,
.ti-btn-outline-primary.dropdown-toggle.show,
.ti-btn-outline-primary:active {
  @apply text-white bg-primary border-primary #{!important};
}
.btn-check+.ti-btn.ti-btn-outline-primary:hover,
.btn-check:active+.ti-btn-outline-primary,
.btn-check:checked+.ti-btn-outline-primary,
.ti-btn-outline-primary.active,
.ti-btn-outline-primary.dropdown-toggle.show,
.ti-btn-outline-primary:active {
  @apply text-white bg-primary border-primary #{!important};
}

.btn-check+.ti-btn.ti-btn-outline-light:hover,
.btn-check:active+.ti-btn-outline-light,
.btn-check:checked+.ti-btn-outline-light,
.ti-btn-outline-light.active,
.ti-btn-outline-light.dropdown-toggle.show,
.ti-btn-outline-light:active {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 bg-light border-light #{!important};
}
.btn-check+.ti-btn.ti-btn-outline-light:hover,
.btn-check:active+.ti-btn-outline-light,
.btn-check:checked+.ti-btn-outline-light,
.ti-btn-outline-light.active,
.ti-btn-outline-light.dropdown-toggle.show,
.ti-btn-outline-light:active {
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 bg-light border-light #{!important};
}
button{
  @apply cursor-pointer #{!important};
}
.ti-btn{
  @apply cursor-pointer #{!important};
}
  /* End:: Social Buttons */