/* Start:: Breadcrumb Styles */


.breadcrumb {
  @apply mb-2 flex;
  .breadcrumb-item {
    a {
      @apply text-primary #{!important};
    }

    &.active {
      @apply text-defaulttextcolor font-medium;
    }
  }
}
.breadcrumb-item+.breadcrumb-item {
  @apply ps-2;
}
.breadcrumb-item+.breadcrumb-item{
  @apply before:content-["/"] before:text-textmuted dark:before:text-textmuted/50 before:me-2;
}
.breadcrumb-example1 {
  .breadcrumb-item+.breadcrumb-item{
    @apply before:content-[''] before:text-textmuted dark:before:text-textmuted/50 before:me-0 #{!important};
  }
}
.breadcrumb-example2 {
  .breadcrumb-item+.breadcrumb-item{
    @apply before:content-["~"] before:text-textmuted dark:before:text-textmuted/50;
  }
}
.breadcrumb-style1 {
  .breadcrumb-item+.breadcrumb-item::before {
    @apply text-textmuted dark:text-textmuted/50 content-["->"];
  }
}
.breadcrumb-style2 {
  .breadcrumb-item+.breadcrumb-item::before {
    @apply text-textmuted dark:text-textmuted/50 content-[''];
  }
}
.breadcrumb-style3{
  .breadcrumb-item+.breadcrumb-item::before {
    @apply text-textmuted dark:text-textmuted/50 content-[''];
  }
}
.embedded-breadcrumb:before {
  @apply opacity-[0.7] before:content-[''];
}
.dark {
  .embedded-breadcrumb:before {
    @apply invert-[1];
  }
}
/* End:: Breadcrumb Styles */

/* Start:: Page-header Breadcrumb Styles */
.page-header-breadcrumb {
  .breadcrumb-item+.breadcrumb-item::before {
    @apply content-["\ea1c"] font-tabler #{!important};
  }
}
.page-header-breadcrumb {
  @apply my-7;
}
.breadcrumb-input {
  @apply min-w-[12.5rem] py-[0.375rem] px-2 #{!important};
}

.page-header-breadcrumb .form-group {
  @apply shadow-defaultshadow;
}

.page-header-breadcrumb .ti-btn {
  @apply shadow-defaultshadow;
}
/* End:: Page-header Breadcrumb Styles */

