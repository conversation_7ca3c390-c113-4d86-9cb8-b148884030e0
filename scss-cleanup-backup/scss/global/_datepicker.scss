/* Start:: React-Datepicker */
.input-group {
    .react-datepicker__input-container input {
      @apply rounded-ss-none rounded-es-none #{!important};
    }
  }
  .react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle,
  .react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle {
    @apply stroke-defaultborder dark:stroke-defaultborder/10 #{!important};
  }
  .react-datepicker__day--keyboard-selected,
  .react-datepicker__month-text--keyboard-selected,
  .react-datepicker__quarter-text--keyboard-selected,
  .react-datepicker__year-text--keyboard-selected {
   @apply bg-primarytint2color #{!important};
  }
  .react-datepicker__time-container
    .react-datepicker__time
    .react-datepicker__time-box
    ul.react-datepicker__time-list
    li.react-datepicker__time-list-item--selected {
      @apply bg-primary #{!important};
  }
  .react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle,
  .react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle {
    @apply fill-defaultborder dark:fill-defaultborder/10 text-defaulttextcolor dark:text-defaulttextcolor/80 stroke-transparent #{!important};
  }
  .page-header-breadcrumb{
    .react-datepicker-popper{
      @apply z-[2] #{!important};
    }
  }
  .react-datepicker-wrapper {
    @apply block #{!important};
  }
  .react-datepicker__input-container input {
    @apply relative flex-grow w-full min-w-0 h-[36px] rounded-sm border-defaultborder dark:border-defaultborder/10 bg-white dark:bg-bodybg py-[0.375rem] px-3;
  }
  .react-datepicker,.react-datepicker__header{
    @apply bg-white dark:bg-bodybg border-defaultborder dark:border-defaultborder/10 #{!important};
  }
  .react-datepicker__current-month, .react-datepicker-time__header, .react-datepicker-year-header,
  .react-datepicker__day-name, .react-datepicker__day, .react-datepicker__time-name{
   @apply text-defaulttextcolor dark:text-defaulttextcolor/80 #{!important};
  }
  .react-datepicker__time-list{
    @apply bg-white dark:bg-bodybg #{!important};
  }
  .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover,
  .react-datepicker__day:hover, .react-datepicker__month-text:hover, .react-datepicker__quarter-text:hover, .react-datepicker__year-text:hover{
   @apply bg-light text-defaulttextcolor dark:text-defaulttextcolor/80 #{!important};
  }
  .react-datepicker__day--selected, .react-datepicker__day--in-selecting-range, .react-datepicker__day--in-range, .react-datepicker__month-text--selected, 
  .react-datepicker__month-text--in-selecting-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--selected,
   .react-datepicker__quarter-text--in-selecting-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--selected, 
   .react-datepicker__year-text--in-selecting-range, .react-datepicker__year-text--in-range{
    @apply bg-primary text-white #{!important};
  }
  .react-datepicker__input-container input:focus-visible{
   @apply outline-none #{!important};
  }
  
.react-datepicker__time-list-item{
  @apply text-defaulttextcolor dark:text-defaulttextcolor/80 #{!important};
}
  /* End:: React-Datepicker */