/* Start::avatars */
.avatar {
    @apply relative h-[2.625rem] w-[2.625rem] inline-flex items-center justify-center rounded-[0.25rem] text-white mb-0 font-medium;
    a.badge:hover {
        @apply text-white;
    }
    img {
        @apply w-full h-full rounded-md;
    }
    svg {
        @apply w-6 h-6;
    }
    &.avatar-rounded {
        @apply rounded-[50%];
        img {
            @apply rounded-[50%];
        }
    }
    &.avatar-radius-0 {
        @apply rounded-none;
        img {
            @apply rounded-none;
        }
    }
    .avatar-badge {
        @apply absolute top-[-4%] end-[-0.375rem] w-[1.4rem] h-[1.4rem] text-[0.625rem] border-customwhite flex items-center justify-center rounded-[50%] border-2 border-solid;
    }
    &.online,&.offline {
        &:before {
            @apply absolute content-[""] w-2 h-2 border-customwhite rounded-[50%] border-2 border-solid end-0 bottom-0;
        }
    }
    &.online:before {
        @apply bg-success;
    }
    &.offline:before {
        @apply bg-gray-500;
    }
    &.avatar-xs {
        @apply w-5 h-5 leading-5 text-[0.65rem];
        .avatar-badge {
            @apply w-4 h-4 leading-4 text-[0.5rem] end-[-0.5rem] p-1 -top-1/4;
        }
    }
    &.avatar-sm {
        @apply w-7 h-7 leading-7 text-xs;
        .avatar-badge {
            @apply w-[1.1rem] h-[1.1rem] leading-[1.1rem] text-[0.5rem] top-[-38%] end-[-0.5rem] p-[0.3rem];
        }
        &.online,&.offline {
            &:before {
                @apply w-2 h-2;
            }
        }    
        svg {
            @apply w-4 h-4;
        }
    }
    &.avatar-md {
        @apply w-10 h-10 leading-10 text-[0.8rem];
        .avatar-badge {
            @apply w-[1.2rem] h-[1.2rem] leading-[1.2rem] text-[0.65rem] top-[-6%] end-[-13%] p-[0.4rem];
        }
        &.online,&.offline {
            &:before {
                @apply w-3 h-3;
            }
        }    
        svg {
            @apply w-5 h-5;
        }
    }
    &.avatar-lg {
        @apply w-12 h-12 leading-[3rem] text-base;
        .avatar-badge {
            @apply top-[-15%] end-[-0.25%];
        }
        &.online,&.offline {
            &:before {
                @apply w-[0.8rem] h-[0.8rem];
            }
        }    
        svg {
            @apply w-6 h-6;
        }
    }
    &.avatar-xl {
        @apply w-16 h-16 leading-[4rem] text-xl;
        .avatar-badge {
            @apply top-[-8%] end-[-0.2%];
        }
        &.online,&.offline {
            &:before {
                @apply w-[0.95rem] h-[0.95rem];
            }
        }    
    }
    &.avatar-xxl {
        @apply w-20 h-20 leading-[5rem] text-2xl;
        .avatar-badge {
            @apply top-[-4%] end-0;
        }
        &.online,&.offline {
            &:before {
                @apply w-[1.05rem] h-[1.05rem] bottom-1;
            }
        }    
    }
}
.avatar-list-stacked {
    @apply p-0;
    .avatar {
        @apply me-[-0.5rem] align-middle transition-transform duration-[ease] delay-200 border-2 border-solid border-transparent;
        &:last-child {
            @apply me-0 #{!important};
        }
        &:hover {
            @apply z-[1] border-customwhite border-2 border-solid scale-[1.15];
        }
    }
}
[dir="rtl"] {
    .avatar-list-stacked {
        .ri-arrow-right-s-line {
            @apply rotate-180;
        }
    }
}
/* End::avatars */