/* Start:: authentication */
.authentication {
  @apply min-h-screen;
  .authentication-brand {
    &.desktop-dark {
      @apply block;
    }
  }
  .form-control {
    @apply pe-10;
  }
  .swiper-button-next,
  .swiper-button-prev {
    @apply bg-[rgba(255,255,255,0.05)] text-[rgba(255,255,255,0.5)] #{!important};
  }
  .swiper-pagination-bullet {
    @apply opacity-10;
  }
  .swiper-pagination-bullet-active {
    @apply opacity-50;
  }
  .google-svg {
    @apply w-3 h-3 me-2;
  }
  .authentication-barrier {
    @apply relative;
    span {
      @apply relative z-[2];
    }
    &:before {
      @apply absolute content-[""] w-[45%] h-0.5 bg-[linear-gradient(to_left,transparent,light)] end-[-35px] z-[1] rounded-[50%] top-2.5;
    }
    &:after {
      @apply absolute content-[""] w-[45%] h-0.5 bg-[linear-gradient(to_left,light,transparent)] start-[-35px] z-[1] rounded-[50%] top-2.5;
    }
  }
  &.coming-soon,
  &.under-maintenance {
    .authentication-cover {
      @apply bg-none bg-white dark:bg-bodybg;
      &:before,
      &:after {
        @apply hidden;
      }
      .aunthentication-cover-content {
        @apply w-full h-full p-12 backdrop-filter-none;
        &:before,
        &:after {
          @apply hidden;
        }
        .coming-soon-time,
        .under-maintenance-time {
          @apply border-primary/20 border-2 border-dashed;
        }
        .authentication-brand {
          @apply w-auto h-8 border-0;
        }
      }
    }
  }
  .coming-soom-image-container,
  .under-maintenance-image-container {
    img {
      @apply w-full h-auto;
    }
  }
  .authentication-cover {
    @apply w-full h-full flex items-center justify-center relative bg-primary/90;
    &:before {
      @apply absolute content-[""] w-full h-full bg-[url(../public/assets/images/media/media-84.jpg)] bg-cover bg-center bg-no-repeat z-[-1] inset-0;
    }
    .aunthentication-cover-content {
      @apply w-9/12 h-80 relative p-8;
    }
  }
  &.authentication-basic {
    .desktop-dark {
      @apply hidden;
    }
  }
  .form-control-lg {
    &#one,
    &#two,
    &#three,
    &#four {
      @apply px-0;
    }
  }
}
@media (min-width: 992px) {
  .aunthentication-cover-content {
    @apply rounded-[0.3rem];
  }
}
[class="dark"] {
  .authentication {
    &.authentication-basic {
      .desktop-white {
        @apply block;
      }
      .desktop-logo {
        @apply hidden;
      }
    }
    .authentication-brand {
      &.desktop-white {
        @apply block;
      }
    }
    &.authentication-cover {
      &.desktop-white {
        @apply block;
      }
    }
  }
}
.authentication {
  .desktop-logo,
  .desktop-white {
    @apply h-[1.7rem] leading-[1.7rem];
  }
  &.authentication-basic {
    .desktop-white {
      @apply hidden;
    }
  }
}

/* Start:: coming soon */
.authentication.coming-soon {
  .form-control {
    @apply pe-4;
  }
}
.coming-soon-main {
  @apply relative bg-white dark:bg-bodybg;
  &:before {
    @apply absolute content-[""] w-full h-full bg-[url(../public/assets/images/media/svg/pattern-2.svg)] bg-cover bg-center bg-no-repeat opacity-10 z-[-1];
  }
}
.footer.authentication-footer {
  @apply bg-transparent shadow-none z-[100] ps-0;
}

.anim, .anim svg {
  @apply absolute w-full h-full opacity-80;
}
.anim path {
  @apply stroke-1;
}
/* End:: coming soon */

.show-password-button {
  @apply absolute p-[0.4rem] end-1 top-0;
}
.authentication.authentication-cover-main {
  .show-password-button {
    @apply p-[0.45rem];
  }
}
.authentication-background {
  @apply relative bg-primary dark:bg-primary #{!important};
  &:before {
    @apply absolute content-[""] w-full h-full bg-[url(../public/assets/images/media/backgrounds/1.jpg)] bg-cover bg-center bg-no-repeat z-[-1] opacity-[0.15];
  }
}
.authentication-cover-image {
  @apply rounded-[50%];
}
.authentication-cover-icon {
  @apply bg-[rgba(255,255,255,0.2)] text-white border-0 #{!important};
  &:hover,&:active,&:focus {
    @apply bg-[rgba(255,255,255,0.2)] text-white border-0 #{!important};
  }
}
.authentication.two-step-verification .form-control {
  @apply pe-3;
} 
.authentication-cover {
  .authentication-cover-logo {
    @apply absolute end-[30px] top-[30px];
  }
}
/* End:: authentication */