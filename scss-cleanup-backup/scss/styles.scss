/*------------------------------------------------------------------
[Master Stylesheet]

Project                            :   Xintra - NextJs App-Router TailwindCss Admin Dashboard Template
Create Date                        :   03/03/2025
Author & Copyright Ownership       :   Spruko Technologies Private Limited 
Author URL                         :   https://themeforest.net/user/spruko
Support	                           :   https://support.spruko.com/
License Details                    :   https://spruko.com/licenses-details
------------------------------------------------------------------*/

/* Table Of Content 
variables
switcher
accordion
alerts
badge
breadcrumb
buttons
cards
dropdown
forms
input_group
list_group
modals
navbar
navs_tabs
pagination
popovers
progress
tables
toast
tooltips
authentication
custom
dashboard_styles
error
header
plugins
ribbons
widgets
closed_menu
detached_menu
double_menu
horizontal
icon_click
icon_hover
icon_overlay
icontext
menu_click
menu_hover
vertical
chat
ecommerce
file-manager
landing
mail
task
avatars
background
border
opacity
typography
*/


/* FONT */
/* Poppins */

@forward "variables";
// @forward "switcher"; // Removed - simplified theme system
@forward "icons";
// @forward "./tailwind/tailwind";



/* CUSTOM */  
@forward "custom/authentication";
@forward "custom/custom";
@forward "custom/dashboard_styles";
@forward "custom/error";
@forward "custom/header";
@forward "custom/plugins";
@forward "custom/ribbons";
@forward "custom/widgets";

/* MENU-STYLES - Simplified to horizontal only */
// @forward "menu-styles/closed_menu"; // Removed - simplified layout system
// @forward "menu-styles/detached_menu"; // Removed - simplified layout system
// @forward "menu-styles/double_menu"; // Removed - simplified layout system
@forward "menu-styles/horizontal"; // Keep - only layout supported
// @forward "menu-styles/icon_click"; // Removed - simplified layout system
// @forward "menu-styles/icon_hover"; // Removed - simplified layout system
// @forward "menu-styles/icon_overlay"; // Removed - simplified layout system
// @forward "menu-styles/icontext"; // Removed - simplified layout system
// @forward "menu-styles/menu_click"; // Removed - simplified layout system
// @forward "menu-styles/menu_hover"; // Removed - simplified layout system
// @forward "menu-styles/vertical"; // Removed - simplified layout system

// /* PAGES */
@forward "pages/chat";
@forward "pages/ecommerce";
@forward "pages/file-manager";
@forward "pages/landing";
@forward "pages/mail";
@forward "pages/task";

/* TAILWIND */
@forward "tailwind/accordion";
@forward "tailwind/alerts";
@forward "tailwind/avatars";
@forward "tailwind/breadcrumb";  
@forward "tailwind/buttons";
@forward "tailwind/cards";
@forward "tailwind/carousels";
@forward "tailwind/components";
@forward "tailwind/dropdown";
@forward "tailwind/forms";
@forward "tailwind/modal";
@forward "tailwind/offcanvas";
@forward "tailwind/pagination";
@forward "tailwind/progress";
@forward "tailwind/tables";
// @forward "tailwind/tailwind";
@forward "tailwind/toast";
@forward "tailwind/tooltip";

/* Global */
@forward "global/datepicker";
@forward "global/select2";
@forward "global/customstyles";
@forward "global/react-listbox";

/* UTILITIES */
@forward "util/avatars";
@forward "util/background";
@forward "util/border";
@forward "util/typography";

/* Google Fonts now loaded via Next.js font optimization */