/* Start:: icon_hover */
[data-nav-layout="horizontal"][data-nav-style="icon-hover"],
[data-nav-style="icon-hover"][data-toggled="icon-hover-closed"] {
    @extend .icon-hover;
}
.icon-hover {
    @media (min-width: 992px) {
        .app-sidebar {
            @apply w-20;
            .main-sidebar {
                @apply overflow-visible h-[90%];
            }
            .main-sidebar-header {
                @apply w-20 justify-center;
                .header-logo {
                    .toggle-logo {
                        @apply block;
                    }
                    .desktop-dark,
                    .desktop-logo,
                    .toggle-dark {
                        @apply hidden #{!important};
                    }
                }
            }
            .category-name,
            .side-menu__label,
            .side-menu__angle {
                @apply hidden;
            }
            .side-menu__icon {
                @apply me-0;
            }
            .slide__category {
                @apply relative px-[1.65rem] py-[1.2rem];
                &:before {
                    @apply content-[""] absolute w-[0.35rem] h-[0.35rem] border border-menuprimecolor opacity-100 rounded-[3.125rem] border-solid start-9 end-0 top-5 bottom-0;
                }
            }
            .simplebar-content-wrapper {
                position: initial;
            }
            .simplebar-mask {
                position: inherit;
            }
            .simplebar-placeholder {
                @apply h-auto #{!important};
            }
        }
        .app-header {
            @apply ps-20;
        }
        .app-content {
            @apply ms-20;
        }
        .slide.side-menu__label1 {
            @apply block border-b-menubordercolor px-4 py-2 border-b border-solid #{!important};
        }
        .slide.has-sub .slide-menu {
            @apply absolute bg-menubg shadow-[0_0_0.375rem_rgba(0,0,0,0.1)] transition-none start-[5rem] top-auto #{!important};
            &.child2,
            &.child3 {
                @apply start-[11.81rem] #{!important};
            }   
        }
        .slide-menu {
            &.child1,
            &.child2,
            &.child3 {
                @apply min-w-[12rem];
                .slide {
                    .side-menu__item {
                        @apply text-start;
                        &:before {
                            @apply hidden;
                        }
                    }
                }
                .side-menu__angle {
                    @apply block end-3 top-[0.65rem];
                }
            }
        }
        .slide.has-sub:hover {
            .slide-menu.child1 {
                @apply block #{!important};
                .slide.has-sub:hover {
                    .slide-menu.child2 {
                        @apply block #{!important};
                        .slide.has-sub:hover {
                            .slide-menu.child3 {
                                @apply block #{!important};
                            }
                        }
                    }
                }
            }
        }
    }
}
[data-nav-layout="horizontal"][data-nav-style="icon-hover"] {
    .mega-menu {
        @apply columns-1;
    }
}
[data-nav-layout="vertical"][data-nav-style="icon-hover"] {
    @media (min-width: 992px) {
        .app-sidebar .main-menu .slide .side-menu__item:hover > .side-menu__item .side-menu__icon {
            @apply text-white;
        }
        &[data-toggled="icon-hover-closed"] {
            .app-sidebar .main-menu{
                >.slide {
                    @apply px-[0.6rem] py-0;
                }
            }
            .app-sidebar {
                .slide .slide-menu {
                    &.child1,&.child2,&.child3 {
                        @apply rounded-[0_0.5rem_0.5rem_0];
                    }
                } 
            }
            &[dir="rtl"] {
                .app-sidebar {
                    .slide .slide-menu {
                        &.child1,&.child2,&.child3 {
                            @apply rounded-[0.5rem_0_0_0.5rem];
                        }
                    } 
                }
            }
            &[class="dark"] {
                .app-sidebar {
                    .main-sidebar-header {
                        .header-logo {
                            .toggle-dark {
                                @apply block;
                            }
                            .desktop-dark,
                            .desktop-logo,
                            .toggle-logo {
                                @apply hidden;
                            }
                        }
                    }
                }
            }
            .app-sidebar {
                @apply absolute;
                .slide-menu {
                    &.child1,
                    &.child2,
                    &.child3 {
                        @apply p-[0.1875rem];
                        li.slide {
                            @apply ps-0;
                            a {
                                @apply rounded-none;
                            }
                        }
                    }
                }
            }
        }
    }
}
@media (min-width: 992px) {
    [data-nav-layout="vertical"] {
        &[data-nav-style="icon-hover"][data-toggled="icon-hover-closed"] { 
            .app-sidebar {
                .slide .side-menu__label {
                    .badge {
                        @apply hidden;
                    }
                }
            }
            &[data-bg-img="bgimg1"],&[data-bg-img="bgimg2"],&[data-bg-img="bgimg3"],&[data-bg-img="bgimg4"],&[data-bg-img="bgimg5"] {
                .app-sidebar {
                    .main-sidebar-header {
                        @apply backdrop-blur-[30px];
                    }
                }
            }
        }
    }
}
@media (min-width: 992px) {
    .icon-hover .app-sidebar .side-menu__icon, [data-nav-layout=horizontal][data-nav-style=icon-hover] .app-sidebar .side-menu__icon, [data-nav-style=icon-hover][data-toggled=icon-hover-closed] .app-sidebar .side-menu__icon {
        @apply text-menuprimecolor;
    }
}

/* End:: icon_hover */