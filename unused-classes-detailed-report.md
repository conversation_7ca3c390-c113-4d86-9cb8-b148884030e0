# Detailed Unused Classes Analysis

Generated on: 2025-07-24T16:43:33.228Z

## 📊 Summary

- **Total configured classes**: 144
- **Total used classes**: 882
- **Total unused classes**: 132

## ❌ Unused Classes by Category


### custom colors (15 classes)

- `text-secondary-btn`
- `border-secondary-btn`
- `text-table-section`
- `border-table-section`
- `text-table-total`
- `border-table-total`
- `text-table-head`
- `border-table-head`
- `border-form-input`
- `text-form-head`
- `border-form-head`
- `text-form-bg`
- `border-form-bg`
- `text-modal-header`
- `border-modal-header`

### custom utilities (22 classes)

- `bg-table-head`
- `bg-form-input`
- `text-form-input`
- `bg-form-head`
- `bg-form-bg`
- `bg-modal-header`
- `bg-textmuted`
- `rounded-input`
- `bg-card-general`
- `bg-card-cashier`
- `bg-texture`
- `bg-card-cashier-texture`
- `bg-card-financial-texture`
- `text-filter-heading`
- `text-filter-label`
- `text-form-label`
- `text-form-placeholder`
- `text-modal-title`
- `text-form-heading`
- `border-filter-heading`
- `border-filter-input`
- `border-table-row`

### custom components (17 classes)

- `dirrtl`
- `dir-rtl`
- `dir-ltr`
- `h1`
- `h2`
- `h3`
- `h4`
- `h5`
- `h6`
- `bg-danger`
- `bg-danger\\/20`
- `border-b-menubordercolor\\/10`
- `border-e-menubordercolor\\/10`
- `from-primary`
- `to-primary`
- `from-secondary`
- `to-secondary`

### gradient stops (78 classes)

- `from-golden-light`
- `to-golden-light`
- `via-golden-light`
- `from-golden`
- `to-golden`
- `via-golden`
- `from-golden-dark`
- `to-golden-dark`
- `via-golden-dark`
- `from-golden-darker`
- `to-golden-darker`
- `via-golden-darker`
- `from-success-light`
- `to-success-light`
- `via-success-light`
- `from-success-dark`
- `to-success-dark`
- `via-success-dark`
- `from-warning-light`
- `to-warning-light`
- `via-warning-light`
- `from-warning-dark`
- `to-warning-dark`
- `via-warning-dark`
- `from-danger-light`
- `to-danger-light`
- `via-danger-light`
- `from-danger-dark`
- `to-danger-dark`
- `via-danger-dark`
- `from-info-light`
- `to-info-light`
- `via-info-light`
- `from-info-dark`
- `to-info-dark`
- `via-info-dark`
- `from-bg-background`
- `to-bg-background`
- `via-bg-background`
- `from-bg-nav`
- `to-bg-nav`
- `via-bg-nav`
- `from-bg-section`
- `to-bg-section`
- `via-bg-section`
- `from-bg-filter`
- `to-bg-filter`
- `via-bg-filter`
- `from-bg-table-section`
- `to-bg-table-section`
- `via-bg-table-section`
- `from-bg-elevated`
- `to-bg-elevated`
- `via-bg-elevated`
- `from-bg-table-total`
- `to-bg-table-total`
- `via-bg-table-total`
- `from-bg-table-head`
- `to-bg-table-head`
- `via-bg-table-head`
- `from-bg-form-input`
- `to-bg-form-input`
- `via-bg-form-input`
- `from-bg-form-head`
- `to-bg-form-head`
- `via-bg-form-head`
- `from-bg-form-bg`
- `to-bg-form-bg`
- `via-bg-form-bg`
- `from-bg-modal-header`
- `to-bg-modal-header`
- `via-bg-modal-header`
- `from-bg-dark`
- `to-bg-dark`
- `via-bg-dark`
- `from-bg-surface`
- `to-bg-surface`
- `via-bg-surface`


## 🔧 Cleanup Recommendations

### High Priority (Safe to Remove)
- `text-form-placeholder`
- `from-golden-light`
- `to-golden-light`
- `via-golden-light`
- `from-golden`
- `to-golden`
- `via-golden`
- `from-golden-dark`
- `to-golden-dark`
- `via-golden-dark`
- `from-golden-darker`
- `to-golden-darker`
- `via-golden-darker`

### Medium Priority (Review Before Removing)
- `text-secondary-btn`
- `border-secondary-btn`
- `text-table-section`
- `border-table-section`
- `text-table-total`
- `border-table-total`
- `text-table-head`
- `border-table-head`
- `border-form-input`
- `text-form-head`
- `border-form-head`
- `text-form-bg`
- `border-form-bg`
- `text-modal-header`
- `border-modal-header`

### Low Priority (Keep for Future Use)
- `bg-table-head`
- `bg-form-input`
- `text-form-input`
- `bg-form-head`
- `bg-form-bg`
- `bg-modal-header`
- `bg-textmuted`
- `rounded-input`
- `bg-card-general`
- `bg-card-cashier`

---
*Generated by Unused Classes Analyzer*
