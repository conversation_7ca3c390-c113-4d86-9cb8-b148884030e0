# Tailwind CSS Audit Report

Generated on: 2025-07-24T16:42:12.273Z

## 📊 Summary Statistics

- **Total files scanned**: 475
- **Total components**: 242
- **Used Tailwind classes**: 916
- **Unused config classes**: 190
- **Dead components**: 1
- **Legacy patterns found**: 150
- **Custom utilities**: 50

## 🎯 Most Used Classes

- `flex`
- `justify-center`
- `xl:col-span-5`
- `lg:col-span-5`
- `md:col-span-6`
- `sm:col-span-7`
- `rounded-md`
- `overflow-hidden`
- `border-defaultborder`
- `dark:border-defaultborder/10`
- `text-center`
- `grid`
- `grid-cols-12`
- `items-center`
- `text-textmuted`
- `dark:text-textmuted/50`
- `!border-s`
- `bg-light`
- `h-full`
- `xl:col-span-4`
- `lg:col-span-3`
- `md:col-span-3`
- `sm:col-span-2`
- `lg:col-span-6`
- `sm:col-span-8`
- `!p-[3rem]`
- `font-normal`
- `text-[14px]`
- `xl:col-span-12`
- `text-defaulttextcolor`
- `text-xs`
- `text-danger`
- `relative`
- `text-primary`
- `grid-cols-11`
- `xl:col-span-2`
- `lg:col-span-2`
- `md:col-span-1`
- `xl:col-span-7`
- `lg:col-span-7`
- `md:col-span-9`
- `text-[1.5rem]`
- `text-[15px]`
- `mb-[3rem]`
- `ti-btn`
- `ti-btn-primary`
- `inline-block`
- `ti-btn-secondary`
- `opacity-70`
- `xl:max-w-[41.66666667%]`

## ❌ Potentially Dead Components

- /home/<USER>/gammastack/betshop/Starterkit/shared/UI/filters/themes/filterThemes.ts

## 🔧 Legacy Patterns to Cleanup

- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(authentication-layout)/authentication/coming-soon/page.tsx**: Custom ti- button classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(authentication-layout)/authentication/create-password/page.tsx**: Complex template literal classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(authentication-layout)/authentication/error/401-error/page.tsx**: Custom ti- button classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(authentication-layout)/authentication/error/404-error/page.tsx**: Custom ti- button classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(authentication-layout)/authentication/error/500-error/page.tsx**: Custom ti- button classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(authentication-layout)/authentication/reset-password/components/ResetPasswordPageClient.tsx**: Complex template literal classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(authentication-layout)/authentication/reset-password/components/ResetPasswordPageClient.tsx**: Custom ti- button classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(authentication-layout)/authentication/sign-up/components/SignUpPageClient.tsx**: Complex template literal classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(authentication-layout)/authentication/sign-up/components/SignUpPageClient.tsx**: Custom ti- button classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(authentication-layout)/authentication/under-maintainance/page.tsx**: Custom ti- button classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(content-layout)/bet-report/components/BetReportPageClient.tsx**: Complex template literal classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(content-layout)/cashier-report/components/CashierReportPageClient.tsx**: Complex template literal classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(content-layout)/financial-report/components/FinancialReportPageClient.tsx**: Complex template literal classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(content-layout)/login-history/components/LoginHistoryPageClient.tsx**: Complex template literal classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(content-layout)/user-management/betting-activity/components/BettingActivityPageClient.tsx**: Custom ti- button classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(content-layout)/user-management/components/EnhancedPageHeader.tsx**: Complex template literal classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(content-layout)/user-management/components/EnhancedPageHeader.tsx**: Custom ti- button classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(content-layout)/user-management/components/UserManagementPageClient.tsx**: Complex template literal classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(content-layout)/user-management/components/UserTableColumns.tsx**: Complex template literal classes
- **/home/<USER>/gammastack/betshop/Starterkit/app/(components)/(content-layout)/user-management/details/[id]/bet-history/components/BetHistoryPageClient.tsx**: Custom ti- button classes

## 🛠️ Custom Utilities

- `dirrtl`
- `dir-rtl`
- `dir-ltr`
- `h1`
- `h2`
- `h3`
- `h4`
- `h5`
- `h6`
- `text-danger`
- `bg-danger`
- `bg-danger\\/20`
- `border-b-menubordercolor\\/10`
- `border-e-menubordercolor\\/10`
- `from-primary`
- `to-primary`
- `from-secondary`
- `to-secondary`
- `bg-textmuted`
- `text-primary`
- `rounded-input`
- `bg-background`
- `bg-nav`
- `bg-section`
- `bg-filter`
- `bg-table-section`
- `bg-elevated`
- `bg-table-total`
- `bg-table-head`
- `bg-form-input`
- `bg-form-head`
- `bg-form-bg`
- `bg-modal-header`
- `bg-card-general`
- `bg-card-cashier`
- `bg-texture`
- `bg-card-general-texture`
- `bg-card-cashier-texture`
- `bg-card-financial-texture`
- `text-filter-heading`
- `text-filter-label`
- `text-filter-placeholder`
- `text-form-input`
- `text-form-label`
- `text-form-placeholder`
- `text-modal-title`
- `text-form-heading`
- `border-filter-heading`
- `border-filter-input`
- `border-table-row`

## 📋 Cleanup Recommendations

1. **Remove unused config classes** (190 found)
2. **Delete dead components** (1 found)
3. **Modernize legacy patterns** (150 found)
4. **Consolidate duplicate utilities**
5. **Optimize config file structure**

---
*Generated by Tailwind CSS Auditor*
