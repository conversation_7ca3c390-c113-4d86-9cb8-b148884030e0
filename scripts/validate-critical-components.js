#!/usr/bin/env node

/**
 * Critical Components Validator
 * 
 * This script validates that critical components maintain their styling
 * after Tailwind cleanup operations.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class CriticalComponentsValidator {
  constructor() {
    this.projectRoot = process.cwd();
    this.criticalComponents = [
      {
        path: 'shared/UI/buttons/PrimaryButton.tsx',
        name: 'PrimaryButton',
        criticalClasses: ['bg-golden-button', 'shadow-golden-button', 'hover:shadow-golden-button-hover'],
        description: 'Golden button with multi-layered shadows'
      },
      {
        path: 'shared/UI/tables/SpkTable.tsx',
        name: 'SpkTable',
        criticalClasses: ['text-white', 'font-semibold', 'leading-none', 'text-gray-400'],
        description: 'Table with 8px border-radius and specific typography'
      },
      {
        path: 'shared/UI/components/StatusBadge.tsx',
        name: 'StatusBadge',
        criticalClasses: ['bg-success-notification', 'text-success-message', 'bg-error-notification'],
        description: 'Status badges with specific color schemes'
      },
      {
        path: 'shared/UI/modals/BaseModal.tsx',
        name: 'BaseModal',
        criticalClasses: ['bg-table-section', 'bg-elevated', 'border-border-secondary'],
        description: 'Modal with layered background system'
      },
      {
        path: 'shared/UI/filters/GlobalFilterSection.tsx',
        name: 'GlobalFilterSection',
        criticalClasses: ['bg-form-input', 'text-secondary', 'bg-golden-button'],
        description: 'Filter section with form styling'
      }
    ];
    this.validationResults = [];
  }

  // Check if a file exists
  fileExists(filePath) {
    const fullPath = path.join(this.projectRoot, filePath);
    return fs.existsSync(fullPath);
  }

  // Read file content
  readFile(filePath) {
    const fullPath = path.join(this.projectRoot, filePath);
    try {
      return fs.readFileSync(fullPath, 'utf8');
    } catch (error) {
      return null;
    }
  }

  // Check if critical classes are present in component
  validateComponent(component) {
    const content = this.readFile(component.path);
    if (!content) {
      return {
        component: component.name,
        status: 'ERROR',
        message: `File not found: ${component.path}`,
        missingClasses: component.criticalClasses,
        foundClasses: []
      };
    }

    const foundClasses = [];
    const missingClasses = [];

    component.criticalClasses.forEach(className => {
      if (content.includes(className)) {
        foundClasses.push(className);
      } else {
        missingClasses.push(className);
      }
    });

    const status = missingClasses.length === 0 ? 'PASS' : 'FAIL';
    const message = status === 'PASS' 
      ? 'All critical classes found'
      : `Missing ${missingClasses.length} critical classes`;

    return {
      component: component.name,
      path: component.path,
      description: component.description,
      status,
      message,
      foundClasses,
      missingClasses,
      totalClasses: component.criticalClasses.length
    };
  }

  // Validate Tailwind config contains required classes
  validateTailwindConfig() {
    const configContent = this.readFile('tailwind.config.ts');
    if (!configContent) {
      return {
        status: 'ERROR',
        message: 'tailwind.config.ts not found',
        missingDefinitions: []
      };
    }

    const requiredDefinitions = [
      'bg-golden-button',
      'shadow-golden-button',
      'bg-form-input',
      'text-secondary',
      'bg-table-section',
      'bg-elevated'
    ];

    const foundDefinitions = [];
    const missingDefinitions = [];

    requiredDefinitions.forEach(definition => {
      if (configContent.includes(definition)) {
        foundDefinitions.push(definition);
      } else {
        missingDefinitions.push(definition);
      }
    });

    return {
      status: missingDefinitions.length === 0 ? 'PASS' : 'FAIL',
      message: missingDefinitions.length === 0 
        ? 'All required definitions found in config'
        : `Missing ${missingDefinitions.length} required definitions`,
      foundDefinitions,
      missingDefinitions,
      totalRequired: requiredDefinitions.length
    };
  }

  // Test build process
  validateBuild() {
    try {
      console.log('🔨 Testing build process...');
      
      // Test TypeScript compilation
      execSync('npm run type-check', { 
        stdio: 'pipe',
        cwd: this.projectRoot 
      });

      // Test Next.js build
      execSync('npm run build:fast', { 
        stdio: 'pipe',
        cwd: this.projectRoot 
      });

      return {
        status: 'PASS',
        message: 'Build completed successfully'
      };
    } catch (error) {
      return {
        status: 'FAIL',
        message: `Build failed: ${error.message}`,
        error: error.toString()
      };
    }
  }

  // Validate CSS generation
  validateCSSGeneration() {
    try {
      // Check if CSS files are generated properly
      const cssFiles = [
        'public/assets/css/styles.css',
        '.next/static/css' // Next.js generated CSS
      ];

      const results = cssFiles.map(cssPath => {
        const fullPath = path.join(this.projectRoot, cssPath);
        const exists = fs.existsSync(fullPath);
        return { path: cssPath, exists };
      });

      const allExist = results.every(result => result.exists);

      return {
        status: allExist ? 'PASS' : 'WARN',
        message: allExist 
          ? 'All CSS files generated successfully'
          : 'Some CSS files may not be generated (this may be normal)',
        results
      };
    } catch (error) {
      return {
        status: 'ERROR',
        message: `CSS validation failed: ${error.message}`
      };
    }
  }

  // Run all validations
  async validate() {
    console.log('🔍 Starting Critical Components Validation...\n');

    // Validate each critical component
    console.log('📋 Validating critical components...');
    for (const component of this.criticalComponents) {
      const result = this.validateComponent(component);
      this.validationResults.push(result);
      
      const statusIcon = result.status === 'PASS' ? '✅' : 
                        result.status === 'FAIL' ? '❌' : '⚠️';
      console.log(`${statusIcon} ${result.component}: ${result.message}`);
    }

    // Validate Tailwind config
    console.log('\n🔧 Validating Tailwind configuration...');
    const configResult = this.validateTailwindConfig();
    const configIcon = configResult.status === 'PASS' ? '✅' : 
                      configResult.status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${configIcon} Config: ${configResult.message}`);

    // Validate build
    console.log('\n🔨 Validating build process...');
    const buildResult = this.validateBuild();
    const buildIcon = buildResult.status === 'PASS' ? '✅' : 
                     buildResult.status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${buildIcon} Build: ${buildResult.message}`);

    // Validate CSS generation
    console.log('\n🎨 Validating CSS generation...');
    const cssResult = this.validateCSSGeneration();
    const cssIcon = cssResult.status === 'PASS' ? '✅' : 
                   cssResult.status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${cssIcon} CSS: ${cssResult.message}`);

    // Generate report
    this.generateValidationReport({
      components: this.validationResults,
      config: configResult,
      build: buildResult,
      css: cssResult
    });
  }

  // Generate validation report
  generateValidationReport(results) {
    const timestamp = new Date().toISOString();
    const totalComponents = results.components.length;
    const passedComponents = results.components.filter(r => r.status === 'PASS').length;
    const failedComponents = results.components.filter(r => r.status === 'FAIL').length;

    const reportContent = `# Critical Components Validation Report

Generated on: ${timestamp}

## 📊 Summary

- **Total components validated**: ${totalComponents}
- **Passed**: ${passedComponents}
- **Failed**: ${failedComponents}
- **Config validation**: ${results.config.status}
- **Build validation**: ${results.build.status}
- **CSS validation**: ${results.css.status}

## 🧪 Component Validation Results

${results.components.map(result => `
### ${result.component} ${result.status === 'PASS' ? '✅' : '❌'}

- **Path**: \`${result.path}\`
- **Description**: ${result.description}
- **Status**: ${result.status}
- **Message**: ${result.message}
- **Found Classes**: ${result.foundClasses.length}/${result.totalClasses}

${result.missingClasses.length > 0 ? `
**Missing Classes**:
${result.missingClasses.map(cls => `- \`${cls}\``).join('\n')}
` : ''}
`).join('')}

## 🔧 Configuration Validation

- **Status**: ${results.config.status}
- **Message**: ${results.config.message}

${results.config.missingDefinitions && results.config.missingDefinitions.length > 0 ? `
**Missing Definitions**:
${results.config.missingDefinitions.map(def => `- \`${def}\``).join('\n')}
` : ''}

## 🔨 Build Validation

- **Status**: ${results.build.status}
- **Message**: ${results.build.message}

## 🎨 CSS Validation

- **Status**: ${results.css.status}
- **Message**: ${results.css.message}

## 🚨 Action Items

${failedComponents > 0 || results.config.status === 'FAIL' || results.build.status === 'FAIL' ? `
**Critical Issues Found**:
${results.components.filter(r => r.status === 'FAIL').map(r => `- Fix missing classes in ${r.component}`).join('\n')}
${results.config.status === 'FAIL' ? '- Fix missing Tailwind config definitions' : ''}
${results.build.status === 'FAIL' ? '- Fix build errors before proceeding' : ''}

**Recommended Actions**:
1. Review and restore missing critical classes
2. Verify Tailwind config contains all required definitions
3. Test build process thoroughly
4. Run visual regression tests
` : `
**All Validations Passed** ✅

The cleanup appears to be successful with no critical issues detected.
`}

---
*Generated by Critical Components Validator*
`;

    const reportPath = path.join(this.projectRoot, 'critical-components-validation-report.md');
    fs.writeFileSync(reportPath, reportContent);
    
    console.log(`\n✅ Validation complete! Report saved to: ${reportPath}`);
    
    // Summary
    const overallStatus = failedComponents === 0 && 
                         results.config.status !== 'FAIL' && 
                         results.build.status !== 'FAIL' ? 'PASS' : 'FAIL';
    
    console.log(`\n📊 Overall Status: ${overallStatus === 'PASS' ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Components: ${passedComponents}/${totalComponents} passed`);
    console.log(`   Config: ${results.config.status}`);
    console.log(`   Build: ${results.build.status}`);
    console.log(`   CSS: ${results.css.status}`);

    return overallStatus === 'PASS';
  }
}

// Run the validation
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new CriticalComponentsValidator();
  validator.validate().catch(console.error);
}

export default CriticalComponentsValidator;
