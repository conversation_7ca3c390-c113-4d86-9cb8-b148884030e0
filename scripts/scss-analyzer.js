#!/usr/bin/env node

/**
 * Comprehensive SCSS Analysis Script
 * 
 * This script performs a complete analysis of SCSS files and their usage:
 * 1. Scans all SCSS files in public/assets/scss
 * 2. Extracts classes, variables, mixins, and imports
 * 3. Searches codebase for usage patterns
 * 4. Identifies unused SCSS code
 * 5. Generates cleanup recommendations
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SCSSAnalyzer {
  constructor() {
    this.projectRoot = process.cwd();
    this.scssDirectory = path.join(this.projectRoot, 'public/assets/scss');
    
    // Storage for extracted SCSS elements
    this.scssFiles = [];
    this.cssClasses = new Map(); // className -> { file, line, usage }
    this.scssVariables = new Map(); // $variable -> { file, line, usage }
    this.scssMixins = new Map(); // mixin -> { file, line, usage }
    this.scssImports = new Map(); // import -> { file, line }
    this.scssKeyframes = new Map(); // @keyframes -> { file, line, usage }
    
    // Usage tracking
    this.usedClasses = new Set();
    this.usedVariables = new Set();
    this.usedMixins = new Set();
    this.usedKeyframes = new Set();
    
    // Component files to search
    this.componentFiles = [];
    
    // Results
    this.unusedClasses = [];
    this.unusedVariables = [];
    this.unusedMixins = [];
    this.unusedFiles = [];
    this.conflictingStyles = [];
  }

  // Scan directory recursively for SCSS files
  scanSCSSDirectory(dir = this.scssDirectory) {
    const files = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          files.push(...this.scanSCSSDirectory(fullPath));
        } else if (item.endsWith('.scss')) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not scan SCSS directory ${dir}: ${error.message}`);
    }
    
    return files;
  }

  // Scan for component files that might use SCSS
  scanComponentFiles() {
    const extensions = ['.tsx', '.ts', '.jsx', '.js', '.mdx', '.scss', '.css'];
    const directories = ['app', 'shared', 'src'];
    
    const files = [];
    
    directories.forEach(dir => {
      const fullDir = path.join(this.projectRoot, dir);
      if (fs.existsSync(fullDir)) {
        files.push(...this.scanDirectory(fullDir, extensions));
      }
    });
    
    return files;
  }

  // Generic directory scanner
  scanDirectory(dir, extensions) {
    const files = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (!['node_modules', '.next', '.git', 'dist', 'build'].includes(item)) {
            files.push(...this.scanDirectory(fullPath, extensions));
          }
        } else if (extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not scan directory ${dir}: ${error.message}`);
    }
    
    return files;
  }

  // Extract SCSS elements from file content
  extractSCSSElements(filePath, content) {
    const relativePath = path.relative(this.projectRoot, filePath);
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const trimmedLine = line.trim();
      
      // Extract CSS classes (.class-name)
      const classMatches = line.match(/\.([a-zA-Z][a-zA-Z0-9_-]*)/g);
      if (classMatches) {
        classMatches.forEach(match => {
          const className = match.substring(1); // Remove the dot
          if (!this.cssClasses.has(className)) {
            this.cssClasses.set(className, []);
          }
          this.cssClasses.get(className).push({
            file: relativePath,
            line: lineNumber,
            context: trimmedLine
          });
        });
      }
      
      // Extract SCSS variables ($variable-name)
      const variableMatches = line.match(/\$([a-zA-Z][a-zA-Z0-9_-]*)/g);
      if (variableMatches) {
        variableMatches.forEach(match => {
          const variableName = match; // Keep the $
          if (!this.scssVariables.has(variableName)) {
            this.scssVariables.set(variableName, []);
          }
          this.scssVariables.get(variableName).push({
            file: relativePath,
            line: lineNumber,
            context: trimmedLine,
            isDefinition: trimmedLine.includes(':') && !trimmedLine.includes('#{')
          });
        });
      }
      
      // Extract mixins (@mixin name or @include name)
      const mixinDefMatches = line.match(/@mixin\s+([a-zA-Z][a-zA-Z0-9_-]*)/g);
      if (mixinDefMatches) {
        mixinDefMatches.forEach(match => {
          const mixinName = match.replace('@mixin ', '').split('(')[0].trim();
          if (!this.scssMixins.has(mixinName)) {
            this.scssMixins.set(mixinName, []);
          }
          this.scssMixins.get(mixinName).push({
            file: relativePath,
            line: lineNumber,
            context: trimmedLine,
            type: 'definition'
          });
        });
      }
      
      const mixinIncMatches = line.match(/@include\s+([a-zA-Z][a-zA-Z0-9_-]*)/g);
      if (mixinIncMatches) {
        mixinIncMatches.forEach(match => {
          const mixinName = match.replace('@include ', '').split('(')[0].trim();
          if (!this.scssMixins.has(mixinName)) {
            this.scssMixins.set(mixinName, []);
          }
          this.scssMixins.get(mixinName).push({
            file: relativePath,
            line: lineNumber,
            context: trimmedLine,
            type: 'usage'
          });
        });
      }
      
      // Extract imports (@import, @use, @forward)
      const importMatches = line.match(/@(?:import|use|forward)\s+["']([^"']+)["']/g);
      if (importMatches) {
        importMatches.forEach(match => {
          const importPath = match.match(/["']([^"']+)["']/)[1];
          if (!this.scssImports.has(importPath)) {
            this.scssImports.set(importPath, []);
          }
          this.scssImports.get(importPath).push({
            file: relativePath,
            line: lineNumber,
            context: trimmedLine
          });
        });
      }
      
      // Extract keyframes
      const keyframeMatches = line.match(/@keyframes\s+([a-zA-Z][a-zA-Z0-9_-]*)/g);
      if (keyframeMatches) {
        keyframeMatches.forEach(match => {
          const keyframeName = match.replace('@keyframes ', '').trim();
          if (!this.scssKeyframes.has(keyframeName)) {
            this.scssKeyframes.set(keyframeName, []);
          }
          this.scssKeyframes.get(keyframeName).push({
            file: relativePath,
            line: lineNumber,
            context: trimmedLine
          });
        });
      }
    });
  }

  // Search for usage of SCSS elements in component files
  searchForUsage(filePath, content) {
    // Search for CSS class usage
    const classPatterns = [
      /className\s*=\s*["'`]([^"'`]+)["'`]/g,
      /className\s*=\s*`([^`]+)`/g,
      /class\s*=\s*["']([^"']+)["']/g,
    ];
    
    classPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const classString = match[1];
        if (classString) {
          const classes = classString.split(/\s+/).filter(cls => cls.trim());
          classes.forEach(cls => {
            const cleanClass = cls.trim().replace(/[{}$]/g, '');
            if (this.cssClasses.has(cleanClass)) {
              this.usedClasses.add(cleanClass);
            }
          });
        }
      }
    });
    
    // Search for SCSS variable usage
    for (const variableName of this.scssVariables.keys()) {
      if (content.includes(variableName)) {
        this.usedVariables.add(variableName);
      }
    }
    
    // Search for mixin usage
    for (const mixinName of this.scssMixins.keys()) {
      if (content.includes(`@include ${mixinName}`) || content.includes(mixinName)) {
        this.usedMixins.add(mixinName);
      }
    }
    
    // Search for keyframe usage
    for (const keyframeName of this.scssKeyframes.keys()) {
      if (content.includes(keyframeName)) {
        this.usedKeyframes.add(keyframeName);
      }
    }
  }

  // Identify conflicts between SCSS and Tailwind
  identifyConflicts() {
    // This will be expanded to check for conflicting styles
    // For now, we'll identify classes that might conflict with Tailwind
    const potentialConflicts = [];
    
    for (const className of this.cssClasses.keys()) {
      // Check if class name follows Tailwind patterns
      if (this.looksLikeTailwindClass(className)) {
        potentialConflicts.push({
          className,
          type: 'potential-tailwind-conflict',
          locations: this.cssClasses.get(className)
        });
      }
    }
    
    this.conflictingStyles = potentialConflicts;
  }

  // Check if a class name looks like it could conflict with Tailwind
  looksLikeTailwindClass(className) {
    const tailwindPatterns = [
      /^(bg|text|border|p|m|w|h)-/,
      /^(flex|grid|block|inline|hidden)/,
      /^(rounded|shadow|opacity)/,
      /^(hover|focus|active):/,
      /^(sm|md|lg|xl):/,
    ];
    
    return tailwindPatterns.some(pattern => pattern.test(className));
  }

  // Main analysis function
  async analyze() {
    console.log('🔍 Starting Comprehensive SCSS Analysis...\n');

    // Step 1: Scan all SCSS files
    console.log('📁 Scanning SCSS files...');
    this.scssFiles = this.scanSCSSDirectory();
    console.log(`   Found ${this.scssFiles.length} SCSS files`);

    // Step 2: Extract SCSS elements
    console.log('🔍 Extracting SCSS elements...');
    for (const filePath of this.scssFiles) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        this.extractSCSSElements(filePath, content);
      } catch (error) {
        console.warn(`Warning: Could not process ${filePath}: ${error.message}`);
      }
    }

    console.log(`   Extracted ${this.cssClasses.size} CSS classes`);
    console.log(`   Extracted ${this.scssVariables.size} SCSS variables`);
    console.log(`   Extracted ${this.scssMixins.size} mixins`);
    console.log(`   Extracted ${this.scssImports.size} imports`);

    // Step 3: Scan component files
    console.log('📋 Scanning component files...');
    this.componentFiles = this.scanComponentFiles();
    console.log(`   Found ${this.componentFiles.length} component files`);

    // Step 4: Search for usage
    console.log('🔍 Searching for SCSS usage...');
    for (const filePath of this.componentFiles) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        this.searchForUsage(filePath, content);
      } catch (error) {
        console.warn(`Warning: Could not process ${filePath}: ${error.message}`);
      }
    }

    console.log(`   Found ${this.usedClasses.size} used CSS classes`);
    console.log(`   Found ${this.usedVariables.size} used SCSS variables`);
    console.log(`   Found ${this.usedMixins.size} used mixins`);

    // Step 5: Identify unused elements
    console.log('🔍 Identifying unused elements...');
    this.identifyUnusedElements();

    // Step 6: Identify conflicts
    console.log('⚠️  Identifying potential conflicts...');
    this.identifyConflicts();

    // Step 7: Generate report
    console.log('📝 Generating analysis report...');
    this.generateReport();
  }

  // Identify unused SCSS elements
  identifyUnusedElements() {
    // Unused classes
    for (const className of this.cssClasses.keys()) {
      if (!this.usedClasses.has(className)) {
        this.unusedClasses.push({
          name: className,
          locations: this.cssClasses.get(className)
        });
      }
    }

    // Unused variables (excluding definitions that are used elsewhere)
    for (const variableName of this.scssVariables.keys()) {
      if (!this.usedVariables.has(variableName)) {
        const locations = this.scssVariables.get(variableName);
        const hasDefinition = locations.some(loc => loc.isDefinition);
        if (hasDefinition) {
          this.unusedVariables.push({
            name: variableName,
            locations
          });
        }
      }
    }

    // Unused mixins (only definitions that aren't used)
    for (const mixinName of this.scssMixins.keys()) {
      const locations = this.scssMixins.get(mixinName);
      const hasDefinition = locations.some(loc => loc.type === 'definition');
      const hasUsage = locations.some(loc => loc.type === 'usage') || this.usedMixins.has(mixinName);
      
      if (hasDefinition && !hasUsage) {
        this.unusedMixins.push({
          name: mixinName,
          locations: locations.filter(loc => loc.type === 'definition')
        });
      }
    }
  }

  // Generate comprehensive report
  generateReport() {
    const reportContent = `# SCSS Analysis Report

Generated on: ${new Date().toISOString()}

## 📊 Summary Statistics

- **Total SCSS files**: ${this.scssFiles.length}
- **Total CSS classes defined**: ${this.cssClasses.size}
- **Total SCSS variables defined**: ${this.scssVariables.size}
- **Total mixins defined**: ${this.scssMixins.size}
- **Total imports**: ${this.scssImports.size}

### Usage Statistics

- **Used CSS classes**: ${this.usedClasses.size}
- **Used SCSS variables**: ${this.usedVariables.size}
- **Used mixins**: ${this.usedMixins.size}

### Cleanup Opportunities

- **Unused CSS classes**: ${this.unusedClasses.length}
- **Unused SCSS variables**: ${this.unusedVariables.length}
- **Unused mixins**: ${this.unusedMixins.length}
- **Potential conflicts**: ${this.conflictingStyles.length}

## 📁 SCSS File Inventory

${this.scssFiles.map(file => `- ${path.relative(this.projectRoot, file)}`).join('\n')}

## ❌ Unused CSS Classes (${this.unusedClasses.length})

${this.unusedClasses.slice(0, 50).map(item => 
  `### \`.${item.name}\`\n${item.locations.map(loc => `- ${loc.file}:${loc.line}`).join('\n')}`
).join('\n\n')}

${this.unusedClasses.length > 50 ? `\n*... and ${this.unusedClasses.length - 50} more*` : ''}

## 🔧 Unused SCSS Variables (${this.unusedVariables.length})

${this.unusedVariables.slice(0, 30).map(item => 
  `### \`${item.name}\`\n${item.locations.map(loc => `- ${loc.file}:${loc.line}`).join('\n')}`
).join('\n\n')}

${this.unusedVariables.length > 30 ? `\n*... and ${this.unusedVariables.length - 30} more*` : ''}

## 🎯 Unused Mixins (${this.unusedMixins.length})

${this.unusedMixins.map(item => 
  `### \`@mixin ${item.name}\`\n${item.locations.map(loc => `- ${loc.file}:${loc.line}`).join('\n')}`
).join('\n\n')}

## ⚠️ Potential Conflicts with Tailwind (${this.conflictingStyles.length})

${this.conflictingStyles.slice(0, 20).map(item => 
  `### \`.${item.className}\` - ${item.type}\n${item.locations.map(loc => `- ${loc.file}:${loc.line}`).join('\n')}`
).join('\n\n')}

## 🧹 Cleanup Recommendations

### High Priority (Safe to Remove)
1. **Unused CSS classes**: ${this.unusedClasses.length} classes can be safely removed
2. **Unused SCSS variables**: ${this.unusedVariables.length} variables can be cleaned up
3. **Unused mixins**: ${this.unusedMixins.length} mixins can be removed

### Medium Priority (Review Before Removing)
1. **Potential Tailwind conflicts**: ${this.conflictingStyles.length} classes may conflict with Tailwind
2. **Review import dependencies** before removing files

### Cleanup Strategy
1. Start with unused mixins (lowest risk)
2. Remove unused variables (check for dynamic usage)
3. Remove unused CSS classes (verify no dynamic class generation)
4. Resolve Tailwind conflicts
5. Remove unused SCSS files

---
*Generated by SCSS Analyzer*
`;

    const reportPath = path.join(this.projectRoot, 'scss-analysis-report.md');
    fs.writeFileSync(reportPath, reportContent);
    
    console.log(`\n✅ Analysis complete! Report saved to: ${reportPath}`);
    console.log('\n📊 Summary:');
    console.log(`   SCSS files: ${this.scssFiles.length}`);
    console.log(`   CSS classes: ${this.cssClasses.size} (${this.unusedClasses.length} unused)`);
    console.log(`   SCSS variables: ${this.scssVariables.size} (${this.unusedVariables.length} unused)`);
    console.log(`   Mixins: ${this.scssMixins.size} (${this.unusedMixins.length} unused)`);
    console.log(`   Potential conflicts: ${this.conflictingStyles.length}`);
  }
}

// Run the analysis
if (import.meta.url === `file://${process.argv[1]}`) {
  const analyzer = new SCSSAnalyzer();
  analyzer.analyze().catch(console.error);
}

export default SCSSAnalyzer;
