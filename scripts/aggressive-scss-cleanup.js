#!/usr/bin/env node

/**
 * Aggressive SCSS Cleanup Script
 * 
 * This script performs aggressive cleanup of unused SCSS files by:
 * 1. Analyzing actual usage in components
 * 2. Removing unused imports from styles.scss
 * 3. Removing unused SCSS files
 * 4. Testing the build
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class AggressiveSCSSCleanup {
  constructor() {
    this.projectRoot = process.cwd();
    this.scssDirectory = path.join(this.projectRoot, 'public/assets/scss');
    this.stylesFile = path.join(this.scssDirectory, 'styles.scss');
    this.backupDirectory = path.join(this.projectRoot, 'aggressive-scss-backup');
    
    this.usedClasses = new Set();
    this.removedFiles = [];
    this.removedImports = [];
    this.errors = [];
  }

  // Create comprehensive backup
  createBackup() {
    console.log('📦 Creating comprehensive backup...');
    
    try {
      if (fs.existsSync(this.backupDirectory)) {
        fs.rmSync(this.backupDirectory, { recursive: true });
      }
      fs.mkdirSync(this.backupDirectory, { recursive: true });

      // Backup entire SCSS directory
      this.copyDirectory(this.scssDirectory, path.join(this.backupDirectory, 'scss'));
      
      // Backup critical files
      const criticalFiles = [
        'tailwind.config.ts',
        'app/globals.scss',
        'postcss.config.cjs'
      ];

      criticalFiles.forEach(file => {
        const srcPath = path.join(this.projectRoot, file);
        const destPath = path.join(this.backupDirectory, file);
        
        if (fs.existsSync(srcPath)) {
          fs.mkdirSync(path.dirname(destPath), { recursive: true });
          fs.copyFileSync(srcPath, destPath);
        }
      });

      console.log(`✅ Backup created at: ${this.backupDirectory}`);
      return true;
    } catch (error) {
      console.error(`❌ Backup failed: ${error.message}`);
      return false;
    }
  }

  // Copy directory recursively
  copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }

    const items = fs.readdirSync(src);
    
    items.forEach(item => {
      const srcPath = path.join(src, item);
      const destPath = path.join(dest, item);
      const stat = fs.statSync(srcPath);

      if (stat.isDirectory()) {
        this.copyDirectory(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    });
  }

  // Analyze which SCSS classes are actually used
  analyzeUsedClasses() {
    console.log('🔍 Analyzing used SCSS classes...');

    const componentFiles = this.scanComponentFiles();
    console.log(`   Scanning ${componentFiles.length} component files...`);

    for (const filePath of componentFiles) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        this.extractUsedClasses(content);
      } catch (error) {
        console.warn(`Warning: Could not process ${filePath}: ${error.message}`);
      }
    }

    console.log(`   Found ${this.usedClasses.size} used classes`);
  }

  // Scan for component files
  scanComponentFiles() {
    const extensions = ['.tsx', '.ts', '.jsx', '.js', '.mdx'];
    const directories = ['app', 'shared', 'src'];
    
    const files = [];
    
    directories.forEach(dir => {
      const fullDir = path.join(this.projectRoot, dir);
      if (fs.existsSync(fullDir)) {
        files.push(...this.scanDirectory(fullDir, extensions));
      }
    });
    
    return files;
  }

  // Generic directory scanner
  scanDirectory(dir, extensions) {
    const files = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (!['node_modules', '.next', '.git', 'dist', 'build'].includes(item)) {
            files.push(...this.scanDirectory(fullPath, extensions));
          }
        } else if (extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not scan directory ${dir}: ${error.message}`);
    }
    
    return files;
  }

  // Extract used classes from component content
  extractUsedClasses(content) {
    // Patterns to match className usage
    const patterns = [
      /className\s*=\s*["'`]([^"'`]+)["'`]/g,
      /className\s*=\s*`([^`]+)`/g,
      /class\s*=\s*["']([^"']+)["']/g,
    ];
    
    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const classString = match[1];
        if (classString) {
          const classes = classString.split(/\s+/).filter(cls => cls.trim());
          classes.forEach(cls => {
            const cleanClass = cls.trim().replace(/[{}$]/g, '');
            // Only add classes that look like custom SCSS classes (not Tailwind)
            if (this.looksLikeCustomSCSSClass(cleanClass)) {
              this.usedClasses.add(cleanClass);
            }
          });
        }
      }
    });
  }

  // Check if a class looks like a custom SCSS class (not Tailwind)
  looksLikeCustomSCSSClass(className) {
    // Tailwind patterns to exclude
    const tailwindPatterns = [
      /^(bg|text|border|p|m|w|h|max-w|max-h|min-w|min-h)-/,
      /^(flex|grid|block|inline|hidden|visible)$/,
      /^(rounded|shadow|opacity|z-)/,
      /^(hover|focus|active|disabled|group-hover):/,
      /^(sm|md|lg|xl|2xl):/,
      /^(dark|light):/,
    ];

    // If it matches Tailwind patterns, it's not a custom SCSS class
    if (tailwindPatterns.some(pattern => pattern.test(className))) {
      return false;
    }

    // Custom SCSS class patterns
    const customPatterns = [
      /^[a-zA-Z][a-zA-Z0-9_-]*$/, // Basic class name
      /^ti-/, // Custom ti- prefixed classes
      /^hs-/, // Preline classes
      /^nav-/, // Navigation classes
      /^btn-/, // Button classes
      /^card-/, // Card classes
      /^modal-/, // Modal classes
    ];

    return customPatterns.some(pattern => pattern.test(className));
  }

  // Remove unused page imports and files
  removeUnusedPages() {
    console.log('\n🗑️  Removing unused page files...');

    const pageFiles = [
      'pages/_chat.scss',
      'pages/_ecommerce.scss', 
      'pages/_file-manager.scss',
      'pages/_mail.scss',
      'pages/_task.scss',
      'pages/_landing.scss',
    ];

    // Check if any page-specific classes are used
    const pageSpecificClasses = [
      'chat-', 'ecommerce-', 'file-manager-', 'mail-', 'task-', 'landing-'
    ];

    const hasPageClasses = Array.from(this.usedClasses).some(cls => 
      pageSpecificClasses.some(prefix => cls.startsWith(prefix))
    );

    if (!hasPageClasses) {
      console.log('   No page-specific classes found, removing all page files...');
      
      pageFiles.forEach(file => {
        this.removeFileAndImport(file);
      });
    } else {
      console.log('   Page-specific classes found, keeping page files');
    }
  }

  // Remove unused menu style files
  removeUnusedMenuStyles() {
    console.log('\n🗑️  Removing unused menu style files...');

    const menuFiles = [
      'menu-styles/_closed_menu.scss',
      'menu-styles/_detached_menu.scss',
      'menu-styles/_double_menu.scss',
      'menu-styles/_icon_click.scss',
      'menu-styles/_icon_hover.scss',
      'menu-styles/_icon_overlay.scss',
      'menu-styles/_icontext.scss',
      'menu-styles/_menu_click.scss',
      'menu-styles/_menu_hover.scss',
      'menu-styles/_vertical.scss', // Keep horizontal only
    ];

    // Check if any menu-specific classes are used
    const menuSpecificClasses = [
      'closed-menu', 'detached-menu', 'double-menu', 'icon-click', 
      'icon-hover', 'icon-overlay', 'icontext', 'menu-click', 'menu-hover', 'vertical-menu'
    ];

    const hasMenuClasses = Array.from(this.usedClasses).some(cls => 
      menuSpecificClasses.some(prefix => cls.includes(prefix))
    );

    if (!hasMenuClasses) {
      console.log('   No complex menu classes found, removing unused menu files...');
      
      menuFiles.forEach(file => {
        this.removeFileAndImport(file);
      });
    } else {
      console.log('   Menu-specific classes found, keeping menu files');
    }
  }

  // Remove unused utility files
  removeUnusedUtilities() {
    console.log('\n🗑️  Removing unused utility files...');

    const utilFiles = [
      'util/_avatars.scss',
      'util/_background.scss',
      'util/_border.scss',
    ];

    // Check if any utility-specific classes are used
    const utilitySpecificClasses = [
      'avatar-', 'bg-img', 'border-'
    ];

    const hasUtilityClasses = Array.from(this.usedClasses).some(cls => 
      utilitySpecificClasses.some(prefix => cls.includes(prefix))
    );

    if (!hasUtilityClasses) {
      console.log('   No utility-specific classes found, removing utility files...');
      
      utilFiles.forEach(file => {
        this.removeFileAndImport(file);
      });
    } else {
      console.log('   Utility-specific classes found, keeping utility files');
    }
  }

  // Remove a file and its import
  removeFileAndImport(relativeFile) {
    const filePath = path.join(this.scssDirectory, relativeFile);
    
    if (fs.existsSync(filePath)) {
      try {
        // Remove the file
        fs.unlinkSync(filePath);
        console.log(`   ✅ Removed ${relativeFile}`);
        this.removedFiles.push(relativeFile);

        // Remove import from styles.scss
        this.removeImportFromStyles(relativeFile);
        
      } catch (error) {
        console.error(`   ❌ Failed to remove ${relativeFile}: ${error.message}`);
        this.errors.push({ file: relativeFile, error: error.message });
      }
    }
  }

  // Remove import statement from styles.scss
  removeImportFromStyles(relativeFile) {
    try {
      let content = fs.readFileSync(this.stylesFile, 'utf8');
      const fileName = path.basename(relativeFile, '.scss');
      const fileNameWithoutUnderscore = fileName.startsWith('_') ? fileName.substring(1) : fileName;
      const directory = path.dirname(relativeFile);
      
      // Remove @forward statements
      const forwardPattern = new RegExp(`@forward\\s+["']${directory}/${fileNameWithoutUnderscore}["'];?\\s*\\n?`, 'g');
      const newContent = content.replace(forwardPattern, '');
      
      if (newContent !== content) {
        fs.writeFileSync(this.stylesFile, newContent);
        this.removedImports.push(`${directory}/${fileNameWithoutUnderscore}`);
      }
    } catch (error) {
      console.error(`   ⚠️  Failed to remove import for ${relativeFile}: ${error.message}`);
    }
  }

  // Test build after cleanup
  testBuild() {
    console.log('\n🔨 Testing build after cleanup...');

    try {
      // Test SCSS compilation first
      console.log('   Testing SCSS compilation...');
      execSync('npm run sass', { 
        stdio: 'pipe',
        cwd: this.projectRoot 
      });
      console.log('   ✅ SCSS compilation passed');

      // Test TypeScript compilation
      console.log('   Testing TypeScript compilation...');
      execSync('npm run type-check', { 
        stdio: 'pipe',
        cwd: this.projectRoot 
      });
      console.log('   ✅ TypeScript compilation passed');

      // Test build
      console.log('   Testing Next.js build...');
      execSync('npm run build:fast', { 
        stdio: 'pipe',
        cwd: this.projectRoot 
      });
      console.log('   ✅ Next.js build passed');

      return true;
    } catch (error) {
      console.error(`   ❌ Build failed: ${error.message}`);
      this.errors.push({ 
        file: 'build_test', 
        error: error.message,
        stdout: error.stdout?.toString(),
        stderr: error.stderr?.toString()
      });
      return false;
    }
  }

  // Generate cleanup report
  generateReport() {
    const reportContent = `# Aggressive SCSS Cleanup Report

Generated on: ${new Date().toISOString()}

## 📊 Summary

- **Files removed**: ${this.removedFiles.length}
- **Imports removed**: ${this.removedImports.length}
- **Used classes found**: ${this.usedClasses.size}
- **Errors encountered**: ${this.errors.length}
- **Backup location**: ${this.backupDirectory}

## 🗑️ Removed Files

${this.removedFiles.map(file => `- ${file}`).join('\n')}

## 📋 Removed Imports

${this.removedImports.map(imp => `- @forward "${imp}"`).join('\n')}

## 🎯 Used Classes (Sample)

${Array.from(this.usedClasses).slice(0, 20).map(cls => `- .${cls}`).join('\n')}
${this.usedClasses.size > 20 ? `\n*... and ${this.usedClasses.size - 20} more*` : ''}

## ❌ Errors

${this.errors.length > 0 ? 
  this.errors.map(error => `- **${error.file}**: ${error.error}`).join('\n') : 
  'No errors encountered ✅'
}

## 🔄 Rollback Instructions

If issues are detected, restore from backup:
\`\`\`bash
cp -r ${this.backupDirectory}/scss/* public/assets/scss/
\`\`\`

---
*Generated by Aggressive SCSS Cleanup*
`;

    const reportPath = path.join(this.projectRoot, 'aggressive-scss-cleanup-report.md');
    fs.writeFileSync(reportPath, reportContent);
    
    console.log(`\n📋 Cleanup report saved to: ${reportPath}`);
  }

  // Main execution function
  async execute() {
    console.log('🚀 Starting Aggressive SCSS Cleanup...\n');

    // Step 1: Create backup
    if (!this.createBackup()) {
      console.error('❌ Cannot proceed without backup');
      return false;
    }

    // Step 2: Analyze used classes
    this.analyzeUsedClasses();

    // Step 3: Remove unused files by category
    this.removeUnusedPages();
    this.removeUnusedMenuStyles();
    this.removeUnusedUtilities();

    // Step 4: Test build
    const buildPassed = this.testBuild();

    // Step 5: Generate report
    this.generateReport();

    console.log('\n✅ Aggressive SCSS cleanup complete!');
    console.log(`   Files removed: ${this.removedFiles.length}`);
    console.log(`   Imports removed: ${this.removedImports.length}`);
    console.log(`   Used classes: ${this.usedClasses.size}`);
    console.log(`   Errors: ${this.errors.length}`);
    console.log(`   Build status: ${buildPassed ? '✅ PASSED' : '❌ FAILED'}`);

    if (!buildPassed) {
      console.log('\n⚠️  Build failed. Restore from backup:');
      console.log(`   cp -r ${this.backupDirectory}/scss/* public/assets/scss/`);
    }

    return buildPassed;
  }
}

// Run the cleanup
if (import.meta.url === `file://${process.argv[1]}`) {
  const cleanup = new AggressiveSCSSCleanup();
  cleanup.execute().catch(console.error);
}

export default AggressiveSCSSCleanup;
