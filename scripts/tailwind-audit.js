#!/usr/bin/env node

/**
 * Comprehensive Tailwind CSS Audit Script
 *
 * This script performs a full audit of Tailwind CSS usage across the codebase:
 * 1. Scans all files for Tailwind class usage
 * 2. Analyzes tailwind.config.ts for custom configurations
 * 3. Identifies unused classes and configurations
 * 4. Finds dead components
 * 5. Generates cleanup recommendations
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class TailwindAuditor {
  constructor() {
    this.projectRoot = process.cwd();
    this.usedClasses = new Set();
    this.configClasses = new Set();
    this.componentFiles = [];
    this.deadComponents = [];
    this.customUtilities = new Set();
    this.legacyPatterns = [];
    this.report = {
      totalFiles: 0,
      totalComponents: 0,
      usedClasses: 0,
      unusedConfigClasses: 0,
      deadComponents: 0,
      legacyPatterns: 0,
      customUtilities: 0
    };
  }

  // Scan directory recursively for files
  scanDirectory(dir, extensions = ['.tsx', '.ts', '.jsx', '.js', '.mdx']) {
    const files = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // Skip node_modules and .next directories
          if (!['node_modules', '.next', '.git', 'dist', 'build'].includes(item)) {
            files.push(...this.scanDirectory(fullPath, extensions));
          }
        } else if (extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not scan directory ${dir}: ${error.message}`);
    }
    
    return files;
  }

  // Extract Tailwind classes from file content
  extractTailwindClasses(content) {
    const classes = new Set();
    
    // Patterns to match className usage
    const patterns = [
      // Standard className="..."
      /className\s*=\s*["'`]([^"'`]+)["'`]/g,
      // Template literals with className
      /className\s*=\s*`([^`]+)`/g,
      // clsx, classnames, or similar utilities
      /(?:clsx|classnames|cn)\s*\(\s*["'`]([^"'`]+)["'`]/g,
      // Conditional classes in template literals
      /\$\{[^}]*["'`]([^"'`]*(?:bg-|text-|border-|p-|m-|w-|h-|flex|grid|rounded)[^"'`]*)["'`][^}]*\}/g,
      // CSS-in-JS or styled-components with Tailwind
      /@apply\s+([^;]+);/g,
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const classString = match[1];
        if (classString) {
          // Split by spaces and filter out empty strings
          const individualClasses = classString
            .split(/\s+/)
            .filter(cls => cls.trim() && this.isTailwindClass(cls.trim()));
          
          individualClasses.forEach(cls => classes.add(cls.trim()));
        }
      }
    });

    return classes;
  }

  // Check if a class looks like a Tailwind class
  isTailwindClass(className) {
    // Common Tailwind prefixes and patterns
    const tailwindPatterns = [
      /^(bg|text|border|p|m|w|h|max-w|max-h|min-w|min-h)-/,
      /^(flex|grid|block|inline|hidden|visible)/,
      /^(rounded|shadow|opacity|z-)/,
      /^(hover|focus|active|disabled|group-hover):/,
      /^(sm|md|lg|xl|2xl):/,
      /^(dark|light):/,
      /^(space-|divide-|ring-|transform|transition)/,
      /^(absolute|relative|fixed|sticky|static)/,
      /^(top|bottom|left|right|inset)-/,
      /^(justify|items|content|self|place)-/,
      /^(overflow|whitespace|break|truncate)/,
      /^(font|leading|tracking|text-)/,
      /^(cursor|select|pointer-events)/,
      /^(animate-|duration-|delay-|ease-)/,
    ];

    // Custom classes from our config
    const customPatterns = [
      /^(bg-|text-|border-)(primary|secondary|success|warning|danger|info)/,
      /^(bg-|text-)(golden|nav|section|filter|elevated|form-)/,
      /^(bg-|text-)(background|table-|modal-|card-)/,
      /^ti-/,  // Custom ti- prefixed classes
      /^hs-/,  // Preline classes
    ];

    return tailwindPatterns.some(pattern => pattern.test(className)) ||
           customPatterns.some(pattern => pattern.test(className)) ||
           className.includes('/') || // Opacity modifiers like bg-red-500/50
           className.includes('[') || // Arbitrary values like w-[100px]
           className.includes('!');   // Important modifier
  }

  // Analyze tailwind.config.ts
  analyzeTailwindConfig() {
    const configPath = path.join(this.projectRoot, 'tailwind.config.ts');
    
    try {
      const configContent = fs.readFileSync(configPath, 'utf8');
      
      // Extract custom colors
      const colorMatches = configContent.match(/["']([a-zA-Z-]+)["']\s*:\s*["']#[0-9a-fA-F]{6}["']/g);
      if (colorMatches) {
        colorMatches.forEach(match => {
          const colorName = match.match(/["']([a-zA-Z-]+)["']/)[1];
          this.configClasses.add(`bg-${colorName}`);
          this.configClasses.add(`text-${colorName}`);
          this.configClasses.add(`border-${colorName}`);
        });
      }

      // Extract custom utilities from addUtilities
      const utilityMatches = configContent.match(/['"]\.([^'"]+)['"]\s*:\s*\{/g);
      if (utilityMatches) {
        utilityMatches.forEach(match => {
          const className = match.match(/['"]\.([^'"]+)['"]/)[1];
          this.customUtilities.add(className);
          this.configClasses.add(className);
        });
      }

      // Extract component classes from addComponents
      const componentMatches = configContent.match(/addComponents\(\{[\s\S]*?\}\)/g);
      if (componentMatches) {
        componentMatches.forEach(match => {
          const componentClasses = match.match(/['"]\.([^'"]+)['"]/g);
          if (componentClasses) {
            componentClasses.forEach(cls => {
              const className = cls.match(/['"]\.([^'"]+)['"]/)[1];
              this.configClasses.add(className);
            });
          }
        });
      }

    } catch (error) {
      console.warn(`Warning: Could not analyze tailwind.config.ts: ${error.message}`);
    }
  }

  // Check if a component file is actually used
  isComponentUsed(filePath) {
    const fileName = path.basename(filePath, path.extname(filePath));
    const componentName = fileName.charAt(0).toUpperCase() + fileName.slice(1);
    
    try {
      // Search for imports of this component
      const result = execSync(`grep -r "import.*${componentName}" --include="*.tsx" --include="*.ts" --include="*.jsx" --include="*.js" . || true`, 
        { encoding: 'utf8', cwd: this.projectRoot });
      
      // If no imports found, check for direct usage
      if (!result.trim()) {
        const usageResult = execSync(`grep -r "${componentName}" --include="*.tsx" --include="*.ts" --include="*.jsx" --include="*.js" . || true`, 
          { encoding: 'utf8', cwd: this.projectRoot });
        return usageResult.trim().length > 0;
      }
      
      return result.trim().length > 0;
    } catch (error) {
      return true; // Assume used if we can't check
    }
  }

  // Identify legacy patterns
  identifyLegacyPatterns(content, filePath) {
    const legacyPatterns = [
      { pattern: /rgb\(var\(--[^)]+\)\)/, description: 'CSS variable usage instead of Tailwind colors' },
      { pattern: /className.*\$\{.*\}/, description: 'Complex template literal classes' },
      { pattern: /bg-\w+legacy/, description: 'Legacy color classes' },
      { pattern: /ti-btn-\w+/, description: 'Custom ti- button classes' },
      { pattern: /hs-\w+/, description: 'Preline component classes' },
    ];

    legacyPatterns.forEach(({ pattern, description }) => {
      if (pattern.test(content)) {
        this.legacyPatterns.push({
          file: filePath,
          pattern: pattern.toString(),
          description
        });
      }
    });
  }

  // Main audit function
  async audit() {
    console.log('🔍 Starting Tailwind CSS Audit...\n');

    // Step 1: Analyze tailwind.config.ts
    console.log('📋 Analyzing tailwind.config.ts...');
    this.analyzeTailwindConfig();

    // Step 2: Scan all component files
    console.log('📁 Scanning component files...');
    const allFiles = [
      ...this.scanDirectory(path.join(this.projectRoot, 'app')),
      ...this.scanDirectory(path.join(this.projectRoot, 'shared')),
    ];

    this.report.totalFiles = allFiles.length;

    // Step 3: Extract classes from each file
    console.log('🔍 Extracting Tailwind classes...');
    for (const filePath of allFiles) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const classes = this.extractTailwindClasses(content);
        
        classes.forEach(cls => this.usedClasses.add(cls));
        
        // Check if component is used
        if (filePath.includes('/components/') || filePath.includes('/UI/')) {
          this.componentFiles.push(filePath);
          if (!this.isComponentUsed(filePath)) {
            this.deadComponents.push(filePath);
          }
        }

        // Identify legacy patterns
        this.identifyLegacyPatterns(content, filePath);

      } catch (error) {
        console.warn(`Warning: Could not process ${filePath}: ${error.message}`);
      }
    }

    // Step 4: Generate report
    this.generateReport();
  }

  // Generate comprehensive report
  generateReport() {
    this.report.usedClasses = this.usedClasses.size;
    this.report.unusedConfigClasses = this.configClasses.size - 
      Array.from(this.configClasses).filter(cls => this.usedClasses.has(cls)).length;
    this.report.deadComponents = this.deadComponents.length;
    this.report.legacyPatterns = this.legacyPatterns.length;
    this.report.customUtilities = this.customUtilities.size;
    this.report.totalComponents = this.componentFiles.length;

    const reportContent = this.formatReport();
    
    // Save report to file
    const reportPath = path.join(this.projectRoot, 'tailwind-audit-report.md');
    fs.writeFileSync(reportPath, reportContent);
    
    console.log(`\n✅ Audit complete! Report saved to: ${reportPath}`);
    console.log('\n📊 Summary:');
    console.log(`   Total files scanned: ${this.report.totalFiles}`);
    console.log(`   Total components: ${this.report.totalComponents}`);
    console.log(`   Used Tailwind classes: ${this.report.usedClasses}`);
    console.log(`   Unused config classes: ${this.report.unusedConfigClasses}`);
    console.log(`   Dead components: ${this.report.deadComponents}`);
    console.log(`   Legacy patterns: ${this.report.legacyPatterns}`);
  }

  formatReport() {
    return `# Tailwind CSS Audit Report

Generated on: ${new Date().toISOString()}

## 📊 Summary Statistics

- **Total files scanned**: ${this.report.totalFiles}
- **Total components**: ${this.report.totalComponents}
- **Used Tailwind classes**: ${this.report.usedClasses}
- **Unused config classes**: ${this.report.unusedConfigClasses}
- **Dead components**: ${this.report.deadComponents}
- **Legacy patterns found**: ${this.report.legacyPatterns}
- **Custom utilities**: ${this.report.customUtilities}

## 🎯 Most Used Classes

${Array.from(this.usedClasses).slice(0, 50).map(cls => `- \`${cls}\``).join('\n')}

## ❌ Potentially Dead Components

${this.deadComponents.map(comp => `- ${comp}`).join('\n')}

## 🔧 Legacy Patterns to Cleanup

${this.legacyPatterns.slice(0, 20).map(pattern => 
  `- **${pattern.file}**: ${pattern.description}`
).join('\n')}

## 🛠️ Custom Utilities

${Array.from(this.customUtilities).map(util => `- \`${util}\``).join('\n')}

## 📋 Cleanup Recommendations

1. **Remove unused config classes** (${this.report.unusedConfigClasses} found)
2. **Delete dead components** (${this.report.deadComponents} found)
3. **Modernize legacy patterns** (${this.report.legacyPatterns} found)
4. **Consolidate duplicate utilities**
5. **Optimize config file structure**

---
*Generated by Tailwind CSS Auditor*
`;
  }
}

// Run the audit
if (import.meta.url === `file://${process.argv[1]}`) {
  const auditor = new TailwindAuditor();
  auditor.audit().catch(console.error);
}

export default TailwindAuditor;
