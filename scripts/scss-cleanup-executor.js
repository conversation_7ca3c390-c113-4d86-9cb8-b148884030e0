#!/usr/bin/env node

/**
 * SCSS Cleanup Executor
 * 
 * This script safely executes SCSS cleanup operations with validation
 * and rollback capabilities.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SCSSCleanupExecutor {
  constructor() {
    this.projectRoot = process.cwd();
    this.scssDirectory = path.join(this.projectRoot, 'public/assets/scss');
    this.backupDirectory = path.join(this.projectRoot, 'scss-cleanup-backup');
    this.cleanupLog = [];
    this.errors = [];
  }

  // Create backup of SCSS files
  createBackup() {
    console.log('📦 Creating backup of SCSS files...');
    
    try {
      // Create backup directory
      if (!fs.existsSync(this.backupDirectory)) {
        fs.mkdirSync(this.backupDirectory, { recursive: true });
      }

      // Copy SCSS directory
      this.copyDirectory(this.scssDirectory, path.join(this.backupDirectory, 'scss'));
      
      // Backup other critical files
      const criticalFiles = [
        'tailwind.config.ts',
        'app/globals.scss',
        'postcss.config.cjs'
      ];

      criticalFiles.forEach(file => {
        const srcPath = path.join(this.projectRoot, file);
        const destPath = path.join(this.backupDirectory, file);
        
        if (fs.existsSync(srcPath)) {
          fs.mkdirSync(path.dirname(destPath), { recursive: true });
          fs.copyFileSync(srcPath, destPath);
        }
      });

      console.log(`✅ Backup created at: ${this.backupDirectory}`);
      return true;
    } catch (error) {
      console.error(`❌ Backup failed: ${error.message}`);
      return false;
    }
  }

  // Copy directory recursively
  copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }

    const items = fs.readdirSync(src);
    
    items.forEach(item => {
      const srcPath = path.join(src, item);
      const destPath = path.join(dest, item);
      const stat = fs.statSync(srcPath);

      if (stat.isDirectory()) {
        this.copyDirectory(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    });
  }

  // Phase 2A: Remove unused SCSS files
  removeUnusedSCSSFiles() {
    console.log('\n🗑️  Phase 2A: Removing unused SCSS files...');

    const unusedFiles = [
      // Page-specific files (unused pages)
      'pages/_chat.scss',
      'pages/_ecommerce.scss', 
      'pages/_file-manager.scss',
      'pages/_mail.scss',
      'pages/_task.scss',
      'pages/_landing.scss',
      
      // Menu styles (if using simple menu)
      'menu-styles/_closed_menu.scss',
      'menu-styles/_detached_menu.scss',
      'menu-styles/_double_menu.scss',
      'menu-styles/_icon_click.scss',
      'menu-styles/_icon_hover.scss',
      'menu-styles/_icon_overlay.scss',
      'menu-styles/_icontext.scss',
      'menu-styles/_menu_click.scss',
      'menu-styles/_menu_hover.scss',
      
      // Custom files with low usage
      'custom/_ribbons.scss',
      'custom/_widgets.scss',
      'custom/_error.scss',
      
      // Utility files with potential conflicts
      'util/_avatars.scss',
      'util/_background.scss',
      'util/_border.scss',
    ];

    let removedCount = 0;
    let skippedCount = 0;

    unusedFiles.forEach(relativeFile => {
      const filePath = path.join(this.scssDirectory, relativeFile);
      
      if (fs.existsSync(filePath)) {
        try {
          // Check if file is imported anywhere
          if (this.isFileImported(relativeFile)) {
            console.log(`⚠️  Skipping ${relativeFile} (still imported)`);
            skippedCount++;
            return;
          }

          // Remove the file
          fs.unlinkSync(filePath);
          console.log(`✅ Removed ${relativeFile}`);
          
          this.cleanupLog.push({
            action: 'removed_file',
            file: relativeFile,
            timestamp: new Date().toISOString()
          });
          
          removedCount++;
        } catch (error) {
          console.error(`❌ Failed to remove ${relativeFile}: ${error.message}`);
          this.errors.push({ file: relativeFile, error: error.message });
        }
      } else {
        console.log(`ℹ️  ${relativeFile} does not exist`);
      }
    });

    console.log(`\n📊 Phase 2A Results:`);
    console.log(`   Removed: ${removedCount} files`);
    console.log(`   Skipped: ${skippedCount} files`);
    console.log(`   Errors: ${this.errors.length} files`);

    return removedCount > 0;
  }

  // Check if a SCSS file is imported anywhere
  isFileImported(relativeFile) {
    const fileName = path.basename(relativeFile, '.scss');
    const fileNameWithoutUnderscore = fileName.startsWith('_') ? fileName.substring(1) : fileName;
    
    try {
      // Search for imports in SCSS files
      const result = execSync(
        `grep -r "@\\(import\\|use\\|forward\\)" --include="*.scss" . | grep -E "(${fileName}|${fileNameWithoutUnderscore})" || true`,
        { encoding: 'utf8', cwd: this.projectRoot }
      );

      return result.trim().length > 0;
    } catch (error) {
      // If grep fails, assume it's imported to be safe
      return true;
    }
  }

  // Update main styles.scss to remove references to deleted files
  updateMainStylesFile() {
    console.log('\n📝 Updating main styles.scss file...');

    const stylesPath = path.join(this.scssDirectory, 'styles.scss');
    
    if (!fs.existsSync(stylesPath)) {
      console.log('⚠️  styles.scss not found, skipping update');
      return false;
    }

    try {
      let content = fs.readFileSync(stylesPath, 'utf8');
      const originalContent = content;

      // Remove imports for deleted files
      const removedFiles = this.cleanupLog
        .filter(log => log.action === 'removed_file')
        .map(log => log.file);

      removedFiles.forEach(file => {
        const fileName = path.basename(file, '.scss');
        const fileNameWithoutUnderscore = fileName.startsWith('_') ? fileName.substring(1) : fileName;
        const directory = path.dirname(file);
        
        // Remove @forward statements
        const forwardPattern = new RegExp(`@forward\\s+["']${directory}/${fileNameWithoutUnderscore}["'];?\\s*\\n?`, 'g');
        content = content.replace(forwardPattern, '');
        
        // Remove @import statements  
        const importPattern = new RegExp(`@import\\s+["']${directory}/${fileNameWithoutUnderscore}["'];?\\s*\\n?`, 'g');
        content = content.replace(importPattern, '');
      });

      // Clean up extra newlines
      content = content.replace(/\n\n\n+/g, '\n\n');

      if (content !== originalContent) {
        fs.writeFileSync(stylesPath, content);
        console.log('✅ Updated styles.scss');
        
        this.cleanupLog.push({
          action: 'updated_styles',
          file: 'styles.scss',
          timestamp: new Date().toISOString()
        });
        
        return true;
      } else {
        console.log('ℹ️  No changes needed in styles.scss');
        return false;
      }
    } catch (error) {
      console.error(`❌ Failed to update styles.scss: ${error.message}`);
      this.errors.push({ file: 'styles.scss', error: error.message });
      return false;
    }
  }

  // Test build after cleanup
  testBuild() {
    console.log('\n🔨 Testing build after cleanup...');

    try {
      // Test TypeScript compilation
      console.log('   Testing TypeScript compilation...');
      execSync('npm run type-check', { 
        stdio: 'pipe',
        cwd: this.projectRoot 
      });
      console.log('   ✅ TypeScript compilation passed');

      // Test build
      console.log('   Testing Next.js build...');
      execSync('npm run build:fast', { 
        stdio: 'pipe',
        cwd: this.projectRoot 
      });
      console.log('   ✅ Next.js build passed');

      return true;
    } catch (error) {
      console.error(`   ❌ Build failed: ${error.message}`);
      this.errors.push({ 
        file: 'build_test', 
        error: error.message,
        stdout: error.stdout?.toString(),
        stderr: error.stderr?.toString()
      });
      return false;
    }
  }

  // Rollback changes if needed
  rollback() {
    console.log('\n🔄 Rolling back changes...');

    try {
      // Restore SCSS directory
      if (fs.existsSync(this.backupDirectory)) {
        const backupScssPath = path.join(this.backupDirectory, 'scss');
        if (fs.existsSync(backupScssPath)) {
          // Remove current SCSS directory
          fs.rmSync(this.scssDirectory, { recursive: true, force: true });
          
          // Restore from backup
          this.copyDirectory(backupScssPath, this.scssDirectory);
          
          console.log('✅ SCSS files restored from backup');
          return true;
        }
      }
      
      console.error('❌ Backup not found, cannot rollback');
      return false;
    } catch (error) {
      console.error(`❌ Rollback failed: ${error.message}`);
      return false;
    }
  }

  // Generate cleanup report
  generateReport() {
    const reportContent = `# SCSS Cleanup Execution Report

Generated on: ${new Date().toISOString()}

## 📊 Summary

- **Actions performed**: ${this.cleanupLog.length}
- **Errors encountered**: ${this.errors.length}
- **Backup location**: ${this.backupDirectory}

## 🔧 Actions Performed

${this.cleanupLog.map(log => 
  `- **${log.action}**: ${log.file} (${log.timestamp})`
).join('\n')}

## ❌ Errors Encountered

${this.errors.length > 0 ? 
  this.errors.map(error => 
    `- **${error.file}**: ${error.error}`
  ).join('\n') : 
  'No errors encountered ✅'
}

## 🔄 Rollback Instructions

If issues are detected, run:
\`\`\`bash
node scripts/scss-cleanup-executor.js --rollback
\`\`\`

Or manually restore from: \`${this.backupDirectory}\`

---
*Generated by SCSS Cleanup Executor*
`;

    const reportPath = path.join(this.projectRoot, 'scss-cleanup-report.md');
    fs.writeFileSync(reportPath, reportContent);
    
    console.log(`\n📋 Cleanup report saved to: ${reportPath}`);
  }

  // Main execution function
  async execute(options = {}) {
    console.log('🚀 Starting SCSS Cleanup Execution...\n');

    // Handle rollback request
    if (options.rollback) {
      return this.rollback();
    }

    // Step 1: Create backup
    if (!this.createBackup()) {
      console.error('❌ Cannot proceed without backup');
      return false;
    }

    // Step 2: Remove unused SCSS files
    const filesRemoved = this.removeUnusedSCSSFiles();
    
    if (!filesRemoved) {
      console.log('ℹ️  No files were removed, cleanup complete');
      this.generateReport();
      return true;
    }

    // Step 3: Update main styles file
    this.updateMainStylesFile();

    // Step 4: Test build
    const buildPassed = this.testBuild();

    if (!buildPassed) {
      console.log('\n⚠️  Build failed after cleanup. Consider rollback.');
      console.log('Run: node scripts/scss-cleanup-executor.js --rollback');
    }

    // Step 5: Generate report
    this.generateReport();

    console.log('\n✅ SCSS cleanup execution complete!');
    console.log(`   Files removed: ${this.cleanupLog.filter(l => l.action === 'removed_file').length}`);
    console.log(`   Errors: ${this.errors.length}`);
    console.log(`   Build status: ${buildPassed ? '✅ PASSED' : '❌ FAILED'}`);

    return buildPassed;
  }
}

// Run the cleanup
if (import.meta.url === `file://${process.argv[1]}`) {
  const options = {
    rollback: process.argv.includes('--rollback')
  };
  
  const executor = new SCSSCleanupExecutor();
  executor.execute(options).catch(console.error);
}

export default SCSSCleanupExecutor;
