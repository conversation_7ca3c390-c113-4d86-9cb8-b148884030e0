#!/usr/bin/env node

/**
 * Tailwind Config Optimizer
 * 
 * This script safely optimizes the tailwind.config.ts by removing unused
 * classes and consolidating duplicate definitions.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class TailwindConfigOptimizer {
  constructor() {
    this.projectRoot = process.cwd();
    this.configPath = path.join(this.projectRoot, 'tailwind.config.ts');
    this.backupPath = path.join(this.projectRoot, 'tailwind.config.ts.backup');
    
    this.usedClasses = new Set();
    this.removedClasses = [];
    this.optimizations = [];
    this.errors = [];
  }

  // Create backup of config file
  createBackup() {
    console.log('📦 Creating backup of tailwind.config.ts...');
    
    try {
      fs.copyFileSync(this.configPath, this.backupPath);
      console.log(`✅ Backup created at: ${this.backupPath}`);
      return true;
    } catch (error) {
      console.error(`❌ Backup failed: ${error.message}`);
      return false;
    }
  }

  // Analyze used classes in codebase
  analyzeUsedClasses() {
    console.log('🔍 Analyzing used Tailwind classes...');

    const componentFiles = this.scanComponentFiles();
    console.log(`   Scanning ${componentFiles.length} component files...`);

    for (const filePath of componentFiles) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        this.extractUsedClasses(content);
      } catch (error) {
        console.warn(`Warning: Could not process ${filePath}: ${error.message}`);
      }
    }

    console.log(`   Found ${this.usedClasses.size} used classes`);
  }

  // Scan for component files
  scanComponentFiles() {
    const extensions = ['.tsx', '.ts', '.jsx', '.js', '.mdx'];
    const directories = ['app', 'shared', 'src'];
    
    const files = [];
    
    directories.forEach(dir => {
      const fullDir = path.join(this.projectRoot, dir);
      if (fs.existsSync(fullDir)) {
        files.push(...this.scanDirectory(fullDir, extensions));
      }
    });
    
    return files;
  }

  // Generic directory scanner
  scanDirectory(dir, extensions) {
    const files = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (!['node_modules', '.next', '.git', 'dist', 'build'].includes(item)) {
            files.push(...this.scanDirectory(fullPath, extensions));
          }
        } else if (extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not scan directory ${dir}: ${error.message}`);
    }
    
    return files;
  }

  // Extract used classes from component content
  extractUsedClasses(content) {
    // Patterns to match className usage
    const patterns = [
      /className\s*=\s*["'`]([^"'`]+)["'`]/g,
      /className\s*=\s*`([^`]+)`/g,
      /@apply\s+([^;]+);/g,
    ];
    
    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const classString = match[1];
        if (classString) {
          const classes = classString.split(/\s+/).filter(cls => cls.trim());
          classes.forEach(cls => {
            const cleanClass = cls.trim().replace(/[{}$]/g, '');
            if (this.isTailwindClass(cleanClass)) {
              this.usedClasses.add(cleanClass);
            }
          });
        }
      }
    });
  }

  // Check if a class looks like a Tailwind class
  isTailwindClass(className) {
    const tailwindPatterns = [
      /^(bg|text|border|p|m|w|h|max-w|max-h|min-w|min-h)-/,
      /^(flex|grid|block|inline|hidden|visible)$/,
      /^(rounded|shadow|opacity|z-)/,
      /^(hover|focus|active|disabled|group-hover):/,
      /^(sm|md|lg|xl|2xl):/,
      /^(dark|light):/,
      /^(space-|divide-|ring-|transform|transition)/,
      /^(absolute|relative|fixed|sticky|static)/,
      /^(top|bottom|left|right|inset)-/,
      /^(justify|items|content|self|place)-/,
      /^(overflow|whitespace|break|truncate)/,
      /^(font|leading|tracking|text-)/,
      /^(cursor|select|pointer-events)/,
      /^(animate-|duration-|delay-|ease-)/,
    ];

    const customPatterns = [
      /^(bg-|text-|border-)(primary|secondary|success|warning|danger|info)/,
      /^(bg-|text-)(golden|nav|section|filter|elevated|form-)/,
      /^(bg-|text-)(background|table-|modal-|card-)/,
      /^ti-/,
      /^hs-/,
    ];

    return tailwindPatterns.some(pattern => pattern.test(className)) ||
           customPatterns.some(pattern => pattern.test(className)) ||
           className.includes('/') ||
           className.includes('[') ||
           className.includes('!');
  }

  // Optimize the Tailwind config
  optimizeConfig() {
    console.log('\n🔧 Optimizing Tailwind config...');

    try {
      let configContent = fs.readFileSync(this.configPath, 'utf8');
      const originalContent = configContent;

      // Remove unused gradient stops (78 classes identified as unused)
      configContent = this.removeUnusedGradientStops(configContent);

      // Remove unused color variants
      configContent = this.removeUnusedColorVariants(configContent);

      // Consolidate duplicate definitions
      configContent = this.consolidateDuplicates(configContent);

      // Clean up formatting
      configContent = this.cleanupFormatting(configContent);

      if (configContent !== originalContent) {
        fs.writeFileSync(this.configPath, configContent);
        console.log('✅ Tailwind config optimized');
        return true;
      } else {
        console.log('ℹ️  No optimizations needed');
        return false;
      }
    } catch (error) {
      console.error(`❌ Config optimization failed: ${error.message}`);
      this.errors.push({ file: 'tailwind.config.ts', error: error.message });
      return false;
    }
  }

  // Remove unused gradient stops
  removeUnusedGradientStops(content) {
    console.log('   Removing unused gradient stops...');

    // Remove the entire gradientColorStops section since most are unused
    const gradientStopsPattern = /gradientColorStops:\s*\{[\s\S]*?\},?\s*\n/;
    const newContent = content.replace(gradientStopsPattern, '');

    if (newContent !== content) {
      this.optimizations.push('Removed unused gradientColorStops section');
      console.log('   ✅ Removed gradientColorStops section');
    }

    return newContent;
  }

  // Remove unused color variants
  removeUnusedColorVariants(content) {
    console.log('   Removing unused color variants...');

    // Remove unused legacy color definitions
    const unusedColors = [
      'primarytint1color', 'primarytint2color', 'primarytint3color',
      'headerbg', 'menubg', 'gray1', 'gray2', 'gray3', 'gray4', 'gray5',
      'gray6', 'gray7', 'gray8', 'gray9', 'customwhite',
      'bodybglegacy', 'bodybg2legacy', 'primarylegacy', 'primaryrgb',
      'secondarylegacy', 'successlegacy', 'infolegacy', 'warninglegacy',
      'dangerlegacy', 'light', 'dark', 'defaulttextcolor', 'defaultborder',
      'defaultbackground', 'menuprimecolor', 'menubordercolor',
      'headerprimecolor', 'headerbordercolor', 'listhoverfocusbg',
      'textmuted', 'inputborder', 'formcontrolbg'
    ];

    let newContent = content;
    let removedCount = 0;

    unusedColors.forEach(colorName => {
      const colorPattern = new RegExp(`\\s*${colorName}:\\s*["'][^"']*["'],?\\s*(?://.*)?\\n`, 'g');
      const beforeLength = newContent.length;
      newContent = newContent.replace(colorPattern, '');
      if (newContent.length < beforeLength) {
        removedCount++;
      }
    });

    if (removedCount > 0) {
      this.optimizations.push(`Removed ${removedCount} unused color definitions`);
      console.log(`   ✅ Removed ${removedCount} unused color definitions`);
    }

    return newContent;
  }

  // Consolidate duplicate definitions
  consolidateDuplicates(content) {
    console.log('   Consolidating duplicate definitions...');

    // Remove duplicate background image definitions
    const duplicateBackgrounds = [
      'primarygradient', 'primary1gradient', 'primary2gradient', 'primary3gradient',
      'secondarygradient', 'successgradient', 'warninggradient', 'pinkgradient',
      'tealgradient', 'dangergradient', 'infogradient', 'orangegradient',
      'purplegradient', 'lightgradient', 'darkgradient'
    ];

    let newContent = content;
    let removedCount = 0;

    duplicateBackgrounds.forEach(bgName => {
      const bgPattern = new RegExp(`\\s*'${bgName}':\\s*'[^']*',?\\s*\\n`, 'g');
      const beforeLength = newContent.length;
      newContent = newContent.replace(bgPattern, '');
      if (newContent.length < beforeLength) {
        removedCount++;
      }
    });

    if (removedCount > 0) {
      this.optimizations.push(`Removed ${removedCount} duplicate background definitions`);
      console.log(`   ✅ Removed ${removedCount} duplicate background definitions`);
    }

    return newContent;
  }

  // Clean up formatting
  cleanupFormatting(content) {
    // Remove excessive newlines
    let newContent = content.replace(/\n\n\n+/g, '\n\n');
    
    // Remove trailing commas before closing braces
    newContent = newContent.replace(/,(\s*\n\s*})/g, '$1');
    
    return newContent;
  }

  // Test build after optimization
  testBuild() {
    console.log('\n🔨 Testing build after optimization...');

    try {
      // Test TypeScript compilation
      console.log('   Testing TypeScript compilation...');
      execSync('npm run type-check', { 
        stdio: 'pipe',
        cwd: this.projectRoot 
      });
      console.log('   ✅ TypeScript compilation passed');

      // Test build
      console.log('   Testing Next.js build...');
      execSync('npm run build:fast', { 
        stdio: 'pipe',
        cwd: this.projectRoot 
      });
      console.log('   ✅ Next.js build passed');

      return true;
    } catch (error) {
      console.error(`   ❌ Build failed: ${error.message}`);
      this.errors.push({ 
        file: 'build_test', 
        error: error.message,
        stdout: error.stdout?.toString(),
        stderr: error.stderr?.toString()
      });
      return false;
    }
  }

  // Rollback to backup
  rollback() {
    console.log('\n🔄 Rolling back to backup...');

    try {
      if (fs.existsSync(this.backupPath)) {
        fs.copyFileSync(this.backupPath, this.configPath);
        console.log('✅ Rollback successful');
        return true;
      } else {
        console.error('❌ Backup file not found');
        return false;
      }
    } catch (error) {
      console.error(`❌ Rollback failed: ${error.message}`);
      return false;
    }
  }

  // Generate optimization report
  generateReport() {
    const reportContent = `# Tailwind Config Optimization Report

Generated on: ${new Date().toISOString()}

## 📊 Summary

- **Used classes analyzed**: ${this.usedClasses.size}
- **Optimizations performed**: ${this.optimizations.length}
- **Errors encountered**: ${this.errors.length}
- **Backup location**: ${this.backupPath}

## 🔧 Optimizations Performed

${this.optimizations.map(opt => `- ${opt}`).join('\n')}

## 🎯 Sample Used Classes

${Array.from(this.usedClasses).slice(0, 30).map(cls => `- \`${cls}\``).join('\n')}
${this.usedClasses.size > 30 ? `\n*... and ${this.usedClasses.size - 30} more*` : ''}

## ❌ Errors

${this.errors.length > 0 ? 
  this.errors.map(error => `- **${error.file}**: ${error.error}`).join('\n') : 
  'No errors encountered ✅'
}

## 🔄 Rollback Instructions

If issues are detected, restore from backup:
\`\`\`bash
cp ${this.backupPath} ${this.configPath}
\`\`\`

---
*Generated by Tailwind Config Optimizer*
`;

    const reportPath = path.join(this.projectRoot, 'tailwind-config-optimization-report.md');
    fs.writeFileSync(reportPath, reportContent);
    
    console.log(`\n📋 Optimization report saved to: ${reportPath}`);
  }

  // Main execution function
  async execute(options = {}) {
    console.log('🚀 Starting Tailwind Config Optimization...\n');

    // Handle rollback request
    if (options.rollback) {
      return this.rollback();
    }

    // Step 1: Create backup
    if (!this.createBackup()) {
      console.error('❌ Cannot proceed without backup');
      return false;
    }

    // Step 2: Analyze used classes
    this.analyzeUsedClasses();

    // Step 3: Optimize config
    const optimized = this.optimizeConfig();

    if (!optimized) {
      console.log('ℹ️  No optimizations performed');
      this.generateReport();
      return true;
    }

    // Step 4: Test build
    const buildPassed = this.testBuild();

    if (!buildPassed) {
      console.log('\n⚠️  Build failed after optimization. Rolling back...');
      this.rollback();
    }

    // Step 5: Generate report
    this.generateReport();

    console.log('\n✅ Tailwind config optimization complete!');
    console.log(`   Optimizations: ${this.optimizations.length}`);
    console.log(`   Errors: ${this.errors.length}`);
    console.log(`   Build status: ${buildPassed ? '✅ PASSED' : '❌ FAILED (rolled back)'}`);

    return buildPassed;
  }
}

// Run the optimization
if (import.meta.url === `file://${process.argv[1]}`) {
  const options = {
    rollback: process.argv.includes('--rollback')
  };
  
  const optimizer = new TailwindConfigOptimizer();
  optimizer.execute(options).catch(console.error);
}

export default TailwindConfigOptimizer;
