#!/usr/bin/env node

/**
 * Unused Classes Analyzer
 * 
 * This script provides detailed analysis of unused Tailwind classes
 * and generates specific cleanup recommendations.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class UnusedClassesAnalyzer {
  constructor() {
    this.projectRoot = process.cwd();
    this.usedClasses = new Set();
    this.configClasses = new Map(); // Map to store class -> source mapping
    this.unusedClasses = [];
  }

  // Scan directory recursively for files
  scanDirectory(dir, extensions = ['.tsx', '.ts', '.jsx', '.js', '.mdx']) {
    const files = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (!['node_modules', '.next', '.git', 'dist', 'build'].includes(item)) {
            files.push(...this.scanDirectory(fullPath, extensions));
          }
        } else if (extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not scan directory ${dir}: ${error.message}`);
    }
    
    return files;
  }

  // Extract all used classes from codebase
  extractUsedClasses() {
    const allFiles = [
      ...this.scanDirectory(path.join(this.projectRoot, 'app')),
      ...this.scanDirectory(path.join(this.projectRoot, 'shared')),
    ];

    for (const filePath of allFiles) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const classes = this.extractTailwindClasses(content);
        classes.forEach(cls => this.usedClasses.add(cls));
      } catch (error) {
        console.warn(`Warning: Could not process ${filePath}: ${error.message}`);
      }
    }
  }

  // Extract Tailwind classes from content
  extractTailwindClasses(content) {
    const classes = new Set();
    
    const patterns = [
      /className\s*=\s*["'`]([^"'`]+)["'`]/g,
      /className\s*=\s*`([^`]+)`/g,
      /(?:clsx|classnames|cn)\s*\(\s*["'`]([^"'`]+)["'`]/g,
      /@apply\s+([^;]+);/g,
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const classString = match[1];
        if (classString) {
          const individualClasses = classString
            .split(/\s+/)
            .filter(cls => cls.trim() && this.isTailwindClass(cls.trim()));
          
          individualClasses.forEach(cls => classes.add(cls.trim()));
        }
      }
    });

    return classes;
  }

  // Check if a class looks like a Tailwind class
  isTailwindClass(className) {
    const tailwindPatterns = [
      /^(bg|text|border|p|m|w|h|max-w|max-h|min-w|min-h)-/,
      /^(flex|grid|block|inline|hidden|visible)/,
      /^(rounded|shadow|opacity|z-)/,
      /^(hover|focus|active|disabled|group-hover):/,
      /^(sm|md|lg|xl|2xl):/,
      /^(dark|light):/,
      /^(space-|divide-|ring-|transform|transition)/,
      /^(absolute|relative|fixed|sticky|static)/,
      /^(top|bottom|left|right|inset)-/,
      /^(justify|items|content|self|place)-/,
      /^(overflow|whitespace|break|truncate)/,
      /^(font|leading|tracking|text-)/,
      /^(cursor|select|pointer-events)/,
      /^(animate-|duration-|delay-|ease-)/,
    ];

    const customPatterns = [
      /^(bg-|text-|border-)(primary|secondary|success|warning|danger|info)/,
      /^(bg-|text-)(golden|nav|section|filter|elevated|form-)/,
      /^(bg-|text-)(background|table-|modal-|card-)/,
      /^ti-/,
      /^hs-/,
    ];

    return tailwindPatterns.some(pattern => pattern.test(className)) ||
           customPatterns.some(pattern => pattern.test(className)) ||
           className.includes('/') ||
           className.includes('[') ||
           className.includes('!');
  }

  // Analyze tailwind.config.ts for all defined classes
  analyzeTailwindConfig() {
    const configPath = path.join(this.projectRoot, 'tailwind.config.ts');
    
    try {
      const configContent = fs.readFileSync(configPath, 'utf8');
      
      // Extract custom colors and generate class variants
      this.extractCustomColors(configContent);
      
      // Extract custom utilities
      this.extractCustomUtilities(configContent);
      
      // Extract component classes
      this.extractComponentClasses(configContent);
      
      // Extract gradient stops
      this.extractGradientStops(configContent);

    } catch (error) {
      console.warn(`Warning: Could not analyze tailwind.config.ts: ${error.message}`);
    }
  }

  extractCustomColors(configContent) {
    // Extract colors from the colors object
    const colorSection = configContent.match(/colors:\s*\{([\s\S]*?)\n\s*\}/);
    if (colorSection) {
      const colorContent = colorSection[1];
      
      // Match color definitions like: "primary": "#E1B649"
      const colorMatches = colorContent.match(/["']([a-zA-Z-]+)["']\s*:\s*["']#[0-9a-fA-F]{6}["']/g);
      if (colorMatches) {
        colorMatches.forEach(match => {
          const colorName = match.match(/["']([a-zA-Z-]+)["']/)[1];
          this.configClasses.set(`bg-${colorName}`, 'custom colors');
          this.configClasses.set(`text-${colorName}`, 'custom colors');
          this.configClasses.set(`border-${colorName}`, 'custom colors');
        });
      }

      // Match nested color objects like: primary: { 50: "#FEF7E0", ... }
      const nestedColorMatches = colorContent.match(/([a-zA-Z-]+):\s*\{[\s\S]*?\}/g);
      if (nestedColorMatches) {
        nestedColorMatches.forEach(match => {
          const colorName = match.match(/([a-zA-Z-]+):/)[1];
          const shades = match.match(/(\d+):\s*["']#[0-9a-fA-F]{6}["']/g);
          if (shades) {
            shades.forEach(shade => {
              const shadeNumber = shade.match(/(\d+):/)[1];
              this.configClasses.set(`bg-${colorName}-${shadeNumber}`, 'custom color shades');
              this.configClasses.set(`text-${colorName}-${shadeNumber}`, 'custom color shades');
              this.configClasses.set(`border-${colorName}-${shadeNumber}`, 'custom color shades');
            });
          }
        });
      }
    }
  }

  extractCustomUtilities(configContent) {
    const utilityMatches = configContent.match(/addUtilities\(\{([\s\S]*?)\}\);/g);
    if (utilityMatches) {
      utilityMatches.forEach(match => {
        const utilityClasses = match.match(/['"]\.([^'"]+)['"]\s*:\s*\{/g);
        if (utilityClasses) {
          utilityClasses.forEach(cls => {
            const className = cls.match(/['"]\.([^'"]+)['"]/)[1];
            this.configClasses.set(className, 'custom utilities');
          });
        }
      });
    }
  }

  extractComponentClasses(configContent) {
    const componentMatches = configContent.match(/addComponents\(\{([\s\S]*?)\}\);/g);
    if (componentMatches) {
      componentMatches.forEach(match => {
        const componentClasses = match.match(/['"]([^'"]+)['"]\s*:\s*\{/g);
        if (componentClasses) {
          componentClasses.forEach(cls => {
            const className = cls.match(/['"]([^'"]+)['"]/)[1];
            if (className.startsWith('.')) {
              this.configClasses.set(className.substring(1), 'custom components');
            }
          });
        }
      });
    }
  }

  extractGradientStops(configContent) {
    const gradientSection = configContent.match(/gradientColorStops:\s*\{([\s\S]*?)\}/);
    if (gradientSection) {
      const gradientContent = gradientSection[1];
      const gradientMatches = gradientContent.match(/["']([a-zA-Z-]+)["']\s*:/g);
      if (gradientMatches) {
        gradientMatches.forEach(match => {
          const gradientName = match.match(/["']([a-zA-Z-]+)["']/)[1];
          this.configClasses.set(`from-${gradientName}`, 'gradient stops');
          this.configClasses.set(`to-${gradientName}`, 'gradient stops');
          this.configClasses.set(`via-${gradientName}`, 'gradient stops');
        });
      }
    }
  }

  // Find unused classes
  findUnusedClasses() {
    this.unusedClasses = [];
    
    for (const [className, source] of this.configClasses) {
      if (!this.usedClasses.has(className)) {
        this.unusedClasses.push({ className, source });
      }
    }
  }

  // Generate detailed report
  generateDetailedReport() {
    const reportContent = `# Detailed Unused Classes Analysis

Generated on: ${new Date().toISOString()}

## 📊 Summary

- **Total configured classes**: ${this.configClasses.size}
- **Total used classes**: ${this.usedClasses.size}
- **Total unused classes**: ${this.unusedClasses.length}

## ❌ Unused Classes by Category

${this.generateUnusedClassesByCategory()}

## 🔧 Cleanup Recommendations

### High Priority (Safe to Remove)
${this.getHighPriorityCleanup()}

### Medium Priority (Review Before Removing)
${this.getMediumPriorityCleanup()}

### Low Priority (Keep for Future Use)
${this.getLowPriorityCleanup()}

---
*Generated by Unused Classes Analyzer*
`;

    const reportPath = path.join(this.projectRoot, 'unused-classes-detailed-report.md');
    fs.writeFileSync(reportPath, reportContent);
    
    console.log(`\n✅ Detailed analysis complete! Report saved to: ${reportPath}`);
    console.log(`\n📊 Found ${this.unusedClasses.length} unused classes out of ${this.configClasses.size} configured classes`);
  }

  generateUnusedClassesByCategory() {
    const categories = {};
    
    this.unusedClasses.forEach(({ className, source }) => {
      if (!categories[source]) {
        categories[source] = [];
      }
      categories[source].push(className);
    });

    let output = '';
    for (const [category, classes] of Object.entries(categories)) {
      output += `\n### ${category} (${classes.length} classes)\n\n`;
      classes.forEach(cls => {
        output += `- \`${cls}\`\n`;
      });
    }

    return output;
  }

  getHighPriorityCleanup() {
    const highPriority = this.unusedClasses.filter(({ className }) => 
      className.includes('legacy') || 
      className.includes('old') ||
      className.includes('deprecated')
    );

    return highPriority.length > 0 
      ? highPriority.map(({ className }) => `- \`${className}\``).join('\n')
      : '- No high priority items found';
  }

  getMediumPriorityCleanup() {
    const mediumPriority = this.unusedClasses.filter(({ className, source }) => 
      source === 'custom colors' || 
      source === 'custom color shades'
    );

    return mediumPriority.length > 0 
      ? mediumPriority.slice(0, 20).map(({ className }) => `- \`${className}\``).join('\n')
      : '- No medium priority items found';
  }

  getLowPriorityCleanup() {
    const lowPriority = this.unusedClasses.filter(({ className, source }) => 
      source === 'gradient stops' || 
      source === 'custom utilities'
    );

    return lowPriority.length > 0 
      ? lowPriority.slice(0, 10).map(({ className }) => `- \`${className}\``).join('\n')
      : '- No low priority items found';
  }

  // Main analysis function
  async analyze() {
    console.log('🔍 Starting detailed unused classes analysis...\n');

    console.log('📋 Analyzing tailwind.config.ts...');
    this.analyzeTailwindConfig();

    console.log('📁 Extracting used classes from codebase...');
    this.extractUsedClasses();

    console.log('🔍 Finding unused classes...');
    this.findUnusedClasses();

    console.log('📝 Generating detailed report...');
    this.generateDetailedReport();
  }
}

// Run the analysis
if (import.meta.url === `file://${process.argv[1]}`) {
  const analyzer = new UnusedClassesAnalyzer();
  analyzer.analyze().catch(console.error);
}

export default UnusedClassesAnalyzer;
